[1, ["ecpdLyjvZBwrvm+cedCcQy", "d5THtJ3D1GBZKNSU54OTA4", "a58WHsl5NPFJkVN2rNcFVm", "a2OozcigBAMIfPYEaT3iOm", "edKO34Pc9EnqcPeoNnuFWN", "26cU3kolFHYbauc8c8Lavk", "e4o+vAdhVCgol7KYSs2MoA", "b3l3ruPb9E2KuGAnyzSEfd", "8eTdzJnQdCBYtXRT0wqBR6", "62xCNOInRK47rtH7txUDdg", "b8Z2adLDJCH6H0CUj6+sLc", "2ac3ceNENNWZ7LeiqAX/eM", "1cUGNeiFdJwpKeRgM9Xcw4", "e9IbxwsY9HPKUYWm6Yq359", "a0NUeZ6V1BvpL5OwJv5oJQ", "51WEKtunNBfJdkLYDJbq9B", "1c8Z8QUkZGLKzPIzfQt/DC", "0fGaFgOHBL87HuxkfnYVOx", "6fgBCSDDdPMInvyNlggls2", "a1U5RdJRFMFL57BdJC9H1X", "59b/OzdXFKnbGhxAm4OQGT", "28rZ/hjSFHhKqeVlEG+O59", "59AG6lPt5ENIGe1Jzkqr1g", "4cvc7akbtJvph2G7u+kcsv", "d58gy4+0tNiZR3ZSqjUsXF", "644SkGz5hAaYv7gsaC89gy", "12tQpiaGFJP6c1e+ig7CAD", "d9wiiiTLpFXqhmkskVY/8e", "7bfY1ormRMSpjvisOOomoo", "beEldejUdH9q0C26I/+owM", "78G44bGYpF6rZuY1CWCdUV", "ccTWHCsXtKAI+y0sjJLMYt", "b3E5Cc8l5A9oMVpem3ctVi", "05E2amS3xJvofWA9R4ad/y", "7eNihB1RdB7YyS7f8QPZiV", "15QzHUC6dOsqUt2zBZMCa7", "e3vPf4N8BKWLy76jMZE/a6", "beAf7IKkdFwZZ/DYsZm3eg", "482Hwz3jlFW5gAdMHolNC2", "e8qt0j51xGMZPl9GieHfJJ", "e70OYGm6tIg5WUJIFOQ+z7", "01emlk1j9Fe4AS9HlGdCax", "85iTjwGgNB8JVJE/MSy7wH", "7a/QZLET9IDreTiBfRn2PD", "68HF1XuD1Gp6NToaRHwaVq", "5e9LLalvpPgY6tM3Yyun1l", "18fh+aHzxIz6acJCw1NWwC", "0bd+0K9rxIgKqRbWvBoNVH", "1cYpMorphEJ4IOROt4/2XP", "54uaxIG1JHo724BkfqyTlN", "bcMn4XlzZDBLnu1UbEh83i", "fcjbw9hGhCk5XN0iYsK0Qs", "bdsiJm8wZLEIygNxHmPBTs", "48RF2NaxRBtJxjHHTV31ZH", "3d9ikBk+RMdJvFpsJ2ZUMd", "e1bxbATGFNfLxSO8ZSMbFI", "7ev80Zpo9PEItRTnzdGSrJ", "a2MjXRFdtLlYQ5ouAFv/+R", "12WT8mPK1K7bpWX4/0Oyv7", "36fihzHMZGFYvCdw7bdqxa", "b6KiMJVQpB27fyUJbAYBq2", "a4hybOCo1IlKNMGtfKzRA8", "37Ae2CFoVCjKHBegirn9jt"], ["node", "_spriteFrame", "_parent", "root", "_defaultClip", "scene", "asset", "_textureSetter", "sprite", "curtainCam", "effCam", "game2Cam", "gameCam", "uiCam", "mainCam", "prefabItem", "m_SingleTxt", "m_Finger", "m_<PERSON>", "m_BlackBg", "nodeBlack", "mapNode", "uiNode", "nWrapHomeBuileBottom", "nWrapBottom", "nWrapCenter", "nWrapBlack", "nWrapTop", "target", "m_spr", "m_Camera", "dragonBone", "map<PERSON>ayer", "convertNode", "ground", "prefabFurnitureItem", "materialDown", "materialUp", "gmSpriteFrame", "_N$dragonAsset", "_N$dragonAtlasAsset", "comLoading", "txtLoad", "loading", "nodeProgressIcon", "txtProgress2", "txtTips", "txtUID", "_N$barSprite", "m_spine", "effectPrefab", "prefabBuild", "abVersionJson", "nodScence1Anim", "data", "prefabBuildFurnitureAnimItem"], [["cc.Node", ["_name", "_groupIndex", "_active", "_opacity", "_id", "_parent", "_components", "_contentSize", "_children", "_prefab", "_trs", "_anchorPoint", "_color", "_position", "_scale"], -2, 1, 9, 5, 2, 4, 7, 5, 5, 8, 8], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_enabled", "_srcBlendFactor", "_dstBlendFactor", "node", "_materials", "_spriteFrame"], -3, 1, 3, 6], ["cc.Node", ["_name", "_groupIndex", "_active", "_is3DNode", "_components", "_parent", "_trs", "_contentSize", "_prefab", "_anchorPoint", "_color", "_position", "_scale", "_children"], -1, 2, 1, 7, 5, 4, 5, 5, 8, 8, 12], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_bottom", "_left", "node"], -2, 1], ["cc.Camera", ["_depth", "_clearFlags", "_cullingMask", "_zoomRatio", "_fov", "_farClip", "_ortho", "_alignWithScreen", "_enabled", "node"], -6, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents"], 1, 1, 9], "cc.SpriteFrame", ["cc.Node", ["_name", "_groupIndex", "_prefab", "_contentSize", "_components", "_trs", "_parent", "_children", "_color", "_anchorPoint"], 1, 4, 5, 12, 7, 1, 12, 5, 5], ["cc.PrefabInfo", ["fileId", "sync", "root", "asset"], 1, 1, 6], ["cc.Label", ["_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_styleFlags", "_lineHeight", "_isSystemFontUsed", "_spacingX", "_materials", "node"], -5, 3, 1], ["cc.Node", ["_name", "_groupIndex", "_active", "_id", "_children", "_components", "_contentSize", "_parent", "_prefab", "_anchorPoint", "_trs"], -1, 2, 12, 5, 1, 4, 5, 7], ["cc.Node", ["_name", "_groupIndex", "_parent", "_children", "_components", "_contentSize", "_anchorPoint", "_trs", "_color"], 1, 1, 2, 2, 5, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON>", ["_fitWidth", "_fitHeight", "node", "_designResolution"], 1, 1, 5], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.Scene", ["_name", "_active", "autoReleaseAssets", "_children", "_anchorPoint", "_trs"], 0, 2, 5, 7], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Graphics", ["_lineWidth", "node", "_materials", "_strokeColor"], 2, 1, 3, 5], ["cc.AnimationClip", ["_name", "_duration", "wrapMode", "curveData"], -1], ["c66f86HQWpDf5Y5jknbX2Gb", ["node", "nodeBuild", "prefabFurnitureItem", "nodBgGray", "prefabBuildFurnitureAnimItem"], 3, 1, 1, 6, 1, 6], "cc.Texture2D", ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.Node", ["_name", "_opacity", "_groupIndex", "_parent", "_children", "_components", "_prefab", "_color", "_contentSize"], 0, 1, 9, 9, 4, 5, 5], ["cc.Node", ["_name", "_id", "_children", "_components", "_contentSize", "_trs"], 1, 12, 9, 5, 7], ["d9fe2pnjDFDyIjDox9bbvzn", ["node", "loading", "txtLoad", "comLoading", "prefabBuild"], 3, 1, 1, 1, 1, 6], ["a1721pQJL1BIZVRz/UCHk+q", ["node"], 3, 1], ["9b476PY+WdP27FsO0eOz9o9", ["node", "prefabItem"], 3, 1, 6], ["3a03a6F9PRGA6S+v+DSS5zG", ["node", "prefabItem"], 3, 1, 6], ["ef6f6SX7FRAOrbvCxkiKHw2", ["node", "txtUID", "txtTips", "persistNode", "txtProgress2", "nodeProgressIcon", "abVersionJson"], 3, 1, 1, 1, 1, 1, 1, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Animation", ["node", "_clips"], 3, 1, 12], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["cc.Scene", ["_name", "_active", "_children", "_anchorPoint", "_trs"], 1, 12, 5, 7], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["3b18fGgzSRLhKIQXMnTqGSZ", ["boneResDir", "node", "m_spine"], 2, 1, 1], ["e26dfhMrQ9Cj4AWdwJmTRoy", ["interval", "maxQuantity", "useNodePool", "node", "effectPrefab"], 0, 1, 6], ["baf90GOqAhLOaIrPDeOTIUX", ["node", "loadingNode", "txtDesc"], 3, 1, 1, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_cacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -3, 1, 3], ["58a2cWhAz5BxIGPucAeyd/x", ["node", "mainCam", "uiCam", "gameCam", "game2Cam", "effCam", "curtainCam", "nodScence1Anim"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["0ef3dQzSdtCsI8FYqYpyixe", ["node", "camera"], 3, 1, 1], ["cc.Prefab", ["_name"], 2], ["8fe46htQK1CaJdxt0SOM5WJ", ["node"], 3, 1], ["52647NiAKJMTKuzK6AvrIL2", ["node"], 3, 1], ["1439aNz3MhPNp+PYnHRUToG", ["node", "m_BlackBg", "m_<PERSON>", "m_Finger", "m_SingleTxt"], 3, 1, 1, 1, 1, 1], ["edc1cmRLO9PDovq60JC8sq2", ["node", "uiNode", "mapNode", "nodeBlack", "mainCam", "uiCam", "gameCam", "game2Cam", "effCam", "curtainCam", "gmSpriteFrame"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["87b1cFliTVNJ5pdqEp8Wewz", ["node", "cameras"], 3, 1, 2], ["b7b3d/wT5VKsaFCiNbmWovU", ["node", "nWrapTop", "nWrapBlack", "nWrapCenter", "nWrapBottom", "nWrapHomeBuileBottom"], 3, 1, 1, 1, 1, 1, 1], ["f56a2ntrghJr4mht4DmdGhG", ["node"], 3, 1], ["e2367Hbp1VLSIfFm0zCcMra", ["node", "comPointTipArr"], 3, 1, 2], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["bae14CEqvlGS5gN0EiIUbft", ["layoutSpaceX", "node", "sprite", "spriteFrames"], 2, 1, 1, 3], ["30d1bpwIkxLnb/VRwbm8SJL", ["node", "nodeBuild"], 3, 1, 1], ["73364SIhzxDw6PN2088iv9o", ["node", "m_Camera", "m_spr", "materialDown", "materialUp"], 3, 1, 1, 1, 6, 6], ["aee17ZSSpRCX5TiTyz6+uTU", ["node", "dragonBone"], 3, 1, 1], ["a0e98uL6ktEuKLMr/0JOGAv", ["node", "ground", "camera", "convertNode", "map<PERSON>ayer"], 3, 1, 1, 1, 1, 1], ["cc.Mask", ["_N$inverted", "node", "_materials"], 2, 1, 3], ["cc.RichText", ["_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "node"], -2, 1], ["ed0b2pa0DxLpL0xazLX/bcy", ["node", "redDot"], 3, 1, 1], ["d9691X4pixJ1LsuD1QS7MHL", ["node", "bgImg"], 3, 1, 1], ["b594acXzkNLEpwOkrxlZJXV", ["node"], 3, 1], ["dragonBones.ArmatureDisplay", ["_armatureName", "_preCacheMode", "_armatureKey", "node", "_materials"], 0, 1, 3]], [[8, 0, 2, 2], [2, 0, 5, 4, 6, 2], [1, 6, 7, 8, 1], [4, 2, 1, 0, 9, 4], [12, 0, 1, 2, 2], [1, 1, 0, 6, 7, 8, 3], [0, 0, 5, 6, 10, 2], [4, 2, 1, 0, 4], [3, 0, 5, 2], [0, 0, 5, 6, 9, 7, 10, 2], [29, 0, 1], [0, 0, 5, 6, 2], [0, 0, 5, 6, 9, 7, 11, 10, 2], [0, 0, 2, 1, 5, 6, 12, 7, 11, 10, 4], [0, 0, 1, 5, 6, 7, 10, 3], [0, 0, 5, 2], [2, 0, 1, 5, 4, 7, 3], [14, 0, 1, 2, 3, 2], [21, 0, 1, 3], [0, 0, 5, 6, 9, 7, 2], [1, 0, 6, 7, 8, 2], [14, 1, 2, 3, 1], [0, 0, 1, 5, 8, 6, 9, 7, 10, 3], [0, 0, 2, 1, 5, 8, 6, 7, 10, 4], [0, 0, 1, 5, 8, 6, 9, 3], [0, 0, 1, 5, 6, 9, 12, 7, 3], [0, 0, 2, 1, 5, 6, 9, 7, 4], [8, 2, 1], [13, 0, 1, 2, 3, 3], [1, 6, 7, 1], [1, 2, 6, 7, 2], [16, 0, 1, 2, 3], [49, 0, 1, 2, 3, 4, 4], [50, 0, 1, 2, 3, 2], [0, 0, 1, 5, 8, 9, 7, 10, 3], [0, 0, 2, 5, 9, 3], [0, 0, 4, 5, 6, 3], [0, 0, 5, 8, 9, 7, 10, 2], [0, 0, 1, 5, 8, 6, 7, 3], [0, 0, 2, 1, 5, 6, 12, 7, 11, 4], [3, 0, 3, 5, 3], [1, 0, 6, 7, 2], [1, 1, 0, 6, 7, 3], [15, 0, 1, 3, 4, 5, 3], [39, 0, 1, 1], [17, 0, 1, 2, 3, 2], [17, 1, 2, 1], [0, 0, 4, 8, 6, 7, 10, 3], [0, 0, 5, 8, 9, 2], [0, 0, 1, 4, 8, 6, 9, 7, 4], [0, 0, 5, 8, 6, 7, 2], [0, 0, 8, 6, 7, 11, 10, 2], [0, 0, 1, 8, 6, 9, 7, 3], [0, 0, 1, 8, 6, 7, 11, 3], [0, 0, 5, 8, 6, 10, 2], [0, 0, 5, 8, 9, 7, 2], [0, 0, 1, 5, 6, 9, 12, 7, 10, 3], [0, 0, 2, 1, 5, 8, 6, 9, 7, 10, 4], [0, 0, 1, 5, 7, 10, 3], [0, 0, 1, 5, 6, 9, 7, 11, 3], [7, 0, 1, 6, 4, 2, 3, 9, 5, 3], [7, 0, 6, 4, 2, 3, 5, 2], [10, 0, 1, 4, 5, 6, 9, 3], [2, 0, 1, 13, 4, 9, 3], [2, 0, 5, 4, 2], [2, 0, 5, 4, 8, 7, 6, 2], [23, 0, 1, 2, 3, 4, 5, 3], [11, 0, 1, 2, 3, 4, 5, 6, 7, 3], [3, 0, 1, 5, 3], [8, 0, 1, 2, 3, 3], [8, 1, 2, 3, 2], [33, 0, 1, 2, 2], [4, 1, 0, 9, 3], [4, 8, 2, 1, 0, 9, 5], [5, 1, 0, 2, 3, 3], [5, 0, 2, 3, 2], [5, 0, 2, 2], [5, 2, 1], [43, 0, 1, 2, 3, 4, 1], [44, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [45, 0, 1, 1], [46, 0, 1, 2, 3, 4, 5, 1], [47, 0, 1], [48, 0, 1, 1], [51, 0, 1, 1], [52, 0, 1, 2, 3, 4, 1], [53, 0, 1, 1], [54, 0, 1, 2, 3, 4, 1], [55, 0, 1, 2, 2], [56, 0, 1, 2, 3, 4, 5, 6], [57, 0, 1, 1], [58, 0, 1, 1], [60, 0, 1, 2, 3, 4, 4], [0, 0, 1, 4, 8, 6, 9, 7, 10, 4], [0, 0, 5, 9, 7, 2], [0, 0, 1, 5, 8, 6, 9, 7, 3], [0, 0, 3, 1, 5, 9, 12, 7, 4], [0, 0, 1, 5, 6, 9, 12, 7, 13, 14, 3], [0, 0, 1, 5, 6, 9, 7, 3], [0, 0, 1, 5, 8, 9, 7, 11, 3], [0, 0, 1, 5, 8, 6, 9, 7, 11, 10, 3], [0, 0, 8, 6, 9, 7, 2], [0, 0, 6, 9, 7, 10, 2], [0, 0, 5, 9, 2], [0, 0, 3, 1, 5, 6, 9, 12, 7, 10, 4], [0, 0, 8, 6, 9, 10, 2], [0, 0, 1, 8, 6, 7, 11, 10, 3], [0, 0, 3, 5, 6, 7, 3], [0, 0, 1, 5, 8, 6, 11, 10, 3], [0, 0, 1, 8, 6, 7, 3], [0, 0, 3, 5, 6, 12, 7, 10, 3], [0, 0, 2, 3, 5, 8, 6, 4], [0, 0, 5, 8, 6, 9, 7, 11, 10, 2], [0, 0, 5, 8, 2], [0, 0, 3, 1, 5, 8, 11, 4], [0, 0, 1, 5, 8, 7, 10, 3], [0, 0, 5, 8, 9, 7, 11, 10, 2], [0, 0, 4, 5, 10, 3], [0, 0, 2, 1, 8, 6, 7, 11, 10, 4], [0, 0, 5, 6, 7, 2], [0, 0, 1, 5, 8, 6, 11, 3], [0, 0, 1, 5, 6, 12, 7, 11, 10, 3], [0, 0, 1, 5, 8, 6, 7, 10, 3], [0, 0, 1, 5, 8, 11, 3], [0, 0, 2, 5, 8, 3], [0, 0, 1, 5, 6, 9, 7, 11, 10, 3], [7, 0, 1, 7, 2, 8, 3, 3], [7, 0, 1, 4, 2, 3, 5, 3], [10, 0, 2, 1, 7, 4, 5, 8, 6, 4], [10, 0, 3, 4, 5, 6, 10, 3], [22, 0, 1, 2, 3, 4, 5, 6, 7, 8, 4], [2, 0, 1, 5, 4, 8, 10, 7, 9, 11, 12, 6, 3], [2, 0, 2, 5, 4, 6, 3], [2, 0, 3, 5, 4, 6, 3], [11, 0, 1, 2, 3, 4, 8, 5, 6, 7, 3], [11, 0, 1, 2, 3, 4, 5, 6, 3], [3, 0, 1, 2, 5, 4], [3, 0, 4, 5, 3], [24, 0, 1, 2, 3, 4, 1], [25, 0, 1], [26, 0, 1, 1], [27, 0, 1, 1], [12, 1, 2, 1], [13, 2, 3, 1], [28, 0, 1, 2, 3, 4, 5, 6, 1], [1, 3, 0, 6, 7, 8, 3], [1, 0, 2, 6, 7, 8, 3], [1, 4, 5, 0, 6, 7, 4], [30, 0, 1, 1], [31, 0, 1, 2, 3, 3], [15, 0, 1, 2, 3, 4, 5, 4], [32, 0, 1, 2, 3, 4, 3], [9, 3, 0, 4, 1, 2, 8, 6], [9, 0, 5, 1, 2, 9, 8, 5], [9, 0, 9, 8, 2], [9, 3, 0, 5, 6, 7, 4, 1, 2, 9, 8, 9], [34, 0, 1, 2, 2], [4, 2, 0, 3, 4, 5, 6, 7, 9, 8], [35, 0, 1, 2, 3, 4, 4], [36, 0, 1, 2, 1], [37, 0, 1, 2, 3, 4, 5, 6, 7, 7], [38, 0, 1, 2, 3, 4, 5, 6, 7, 1], [5, 2, 3, 1], [16, 0, 1, 3], [18, 0, 2], [18, 0, 1, 2, 3, 5], [40, 0, 2], [41, 0, 1], [42, 0, 1], [19, 0, 1, 3, 2, 1], [19, 0, 1, 2, 4, 1], [59, 0, 1]], [[[[18, "LoadSceneBak", null], [93, "PersistNode", 6, "09drqoEGZPFqdbr3UwMwVY", [-10, -11, -12], [[136, 45, 750, 1624, -2], [138, -6, -5, -4, -3, 9], [139, -7], [140, -8, 10], [141, -9, 11]], [27, -1], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "<PERSON><PERSON>", "c4yrwaYLNCzrciPLUPaeel", [-20, -21, -22, -23], [[28, true, false, -13, [5, 750, 1334]], [144, -18, -17, -16, 1, -15, -14, 20], [8, 45, -19]], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [126, "content_2", 8, [[-24, [94, "bg", -25, [0, "99KrmJHe1OjJA8N5bXNb7J", 1], [5, 750, 1624]], -26, -27, -28, -29, -30], 1, 4, 1, 1, 1, 1, 1], [0, "c23M+8MA9PN5NfbzWwQhnQ", 1], [4, 4290766324], [5, 1500, 1660]], [128, "touch_loading", false, 6, 1, [-34, -35, -36], [[[8, 45, -31], [10, -32], -33], 4, 4, 1], [0, "4fWsCK90tDvqIjbAK4aaCs", 1], [5, 750, 1334]], [95, "loading", 8, 1, [3], [[8, 45, -37]], [0, "29fhEtAwpILa9WbsmLKwiR", 1], [5, 750, 1334]], [130, "BlackBg", 140, 6, 4, [[96, "click", 190, 6, -40, [0, "24wxhKy7tBZacSDTQxzkqJ", 1], [4, 4278190080], [5, 750, 1800]]], [[10, -38], [5, 1, 0, -39, [1], 2]], [0, "28ushRcnBBX6Pi9dWfErH+", 1], [4, 4278190080], [5, 750, 1800]], [97, "loading", 6, 4, [[20, 0, -41, [3], 4], [21, -42, [6], 5]], [0, "bcs+1+4JROSZMBmoo2Uvr3", 1], [4, 4290963194], [5, 90, 90], [1, 0, 0, 0], [1, 1, 1, 1]], [22, "progressBar", 8, 3, [-46], [[5, 1, 0, -43, [15], 16], [149, 521, 0, -45, -44]], [0, "b5PPmDLvFIjI47MlFMw5es", 1], [5, 527, 39], [0, -436.37, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "New Node", false, [2, 1], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "uid", 8, 3, [[-47, [40, 4, 309.84, -48]], 1, 4], [0, "78UWxhFMNKkZa8YMBRgnXG", 1], [5, 0, 40.32], [0, 1, 0.5], [350.446, -500.00000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [152, "快想办法让杰茜别再受冻……", 28, 1, 1, 1, [8]], [61, "tip", 3, [[11, [71, 3, -49, [4, 4282003510]]], 1, 4], [0, "1597dNw3RKYpK9m6FldU94", 1], [5, 370, 56.4], [0, -389.481, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "Spine_Loading", 3, [-50, -51], [0, "54oSU8O9xPTLt3m+ddg3IO", 1]], [61, "Spine", 13, [[-52, [156, "texture/spine", -54, -53]], 1, 4], [0, "54rmvGTThGpYR+hA20ZpJ2", 1], [5, 1947.02, 3521.52], [18.408, -467.212, 0, 0, 0, 0, 1, 0.5, 0.5, 0.5]], [60, "progressInner", 8, 8, [[[137, 8, 3, -55], -56], 4, 1], [0, "17mQxK3I9LGq6iU9uwKalr", 1], [5, 0, 33], [0, 0, 0.5], [-260.5, 0.597, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "candy", 3, [[29, -57, [17]]], [0, "86msx3fbpEDabASueZuj0C", 1], [5, 55, 59], [-255, -443, 0, 0, 0, 0, 1, 1, 1, 1]], [127, "txtProgress", 8, [[-58, [71, 5, -59, [4, 4282003510]]], 1, 4], [0, "89qypM9NJOw7lXUavtMTUM", 1], [5, 76.48, 73], [0, 0, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [6, "Main Camera", 2, [[72, 7, -1, -60]], [0, 0, 579.3709951317895, 0, 0, 0, 1, 1, 1, 1]], [6, "BgCamera", 2, [[3, 16, 2, 10, -61]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [6, "UICamera", 2, [[3, 2, 2, 40, -62]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [6, "CurtainCamera", 2, [[3, 64, 0, 60, -63]], [0, 0, 336.11257819690115, 0, 0, 0, 1, 1, 1, 1]], [153, 23, 32, 1, 1, 10, [19]], [98, "node_touch", 6, 1, [[158, 0.03, 10, true, -64, 0]], [0, "70Oe9de8lKEpBLmZa1RAF8", 1], [5, 750, 2000]], [131, "desc", 6, 4, [-65], [0, "e9D07tynBO3K+ZBr1yOX6i", 1], [4, 4291028730], [5, 418.92, 50.4], [0, 0.5, 1], [1, 0, 0, 0], [1, 1, 1, 1], [0, -80, 0, 0, 0, 0, 1, 1, 1, 1]], [154, 26, 24, [7]], [159, 4, 7, 25], [9, "beijing", 13, [[41, 0, -66, [12]]], [0, "afyM0ffR1JQbsf1YuN8yd1", 1], [5, 750, 1634], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0.5]], [160, "default", "idle1", 2, 2, false, "idle1", 14, [13]], [42, 1, 0, 15, [14]], [34, "per", 8, 3, [17], [0, "0faKDKNVpAa5PadX1IaweV", 1], [5, 48.04, 36], [0, -435.003, 0, 0, 0, 0, 1, 1, 1, 1]], [155, "0%", 46, 50, false, -1, 1, 1, 1, 17, [18]]], 0, [0, 3, 1, 0, 0, 1, 0, 41, 26, 0, 42, 11, 0, 43, 5, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, -2, 23, 0, -3, 4, 0, 0, 2, 0, 44, 16, 0, 45, 31, 0, 46, 11, 0, 47, 22, 0, 0, 2, 0, 0, 2, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, -4, 21, 0, -1, 13, 0, 2, 3, 0, -3, 12, 0, -4, 8, 0, -5, 16, 0, -6, 30, 0, -7, 10, 0, 0, 4, 0, 0, 4, 0, -3, 26, 0, -1, 6, 0, -2, 7, 0, -3, 24, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 2, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 48, 29, 0, 0, 8, 0, -1, 15, 0, -1, 22, 0, 0, 10, 0, 0, 12, 0, -1, 27, 0, -2, 14, 0, -1, 28, 0, 49, 28, 0, 0, 14, 0, 0, 15, 0, -2, 29, 0, 0, 16, 0, -1, 31, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 23, 0, -1, 25, 0, 0, 27, 0, 5, 9, 1, 2, 9, 2, 2, 9, 3, 2, 5, 11, 0, 12, 17, 2, 30, 66], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29], [50, -1, 1, -1, 1, 4, -1, -1, -1, 51, 15, 15, -1, -1, -1, -1, 1, -1, -1, -1, 52, 1], [38, 0, 39, 0, 40, 21, 21, 0, 0, 17, 41, 42, 0, 43, 0, 0, 44, 0, 0, 0, 45, 46]], [[[18, "HomeSweetPlotScene", null], [66, "<PERSON><PERSON>", "377SojyLdO4ZDj0O4BZ5dq", [[-11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, [15, "ConvertNode", -22], -23], 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1], [[28, true, false, -1, [5, 750, 1334]], [8, 45, -2], [161, -10, -9, -8, -7, -6, -5, -4, -3]], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [99, "guide_sweeter_plot", 6, 1, [-25], [27, -24], [5, 750, 1334], [0, 0.5, 0]], [100, "anims", 6, 2, [-27, -28, -29], [[10, -26]], [0, "4b5tFb/wJHnbsuX5Ho2ECb", 2], [5, 750, 1334], [0, 0.5, 0], [0, -667, 0, 0, 0, 0, 1, 1.05, 1.05, 1]], [101, "1", [-31, -32], [[21, -30, [3], 2]], [0, "83LdbL41BHBZd9rIjL/S0M", 2], [5, 750, 1600]], [43, "New Node", false, [1, -33], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "scene1", 6, 3, [4], [[10, -34]], [0, "4db0DJxGNJlZAwCnTGPZXD", 2], [5, 750, 1334], [0, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [102, "jump", [[2, -35, [5], 6], [74, 0.8, 3, -36, [[31, "58a2cWhAz5BxIGPucAeyd/x", "onSkipPlotAnim", 1]]]], [0, "a0RCh0AVZGv43xAP+GT5v5", 2], [5, 148, 71], [260.725, 1119.747, 0, 0, 0, 0, 1, 1, 1, 0]], [103, "plot_0", 4, [69, "e2JpWEa3xJ7ILwAquV0sov", true, -37, 0]], [35, "plot_1", false, 4, [69, "de7Zb6YFdMr65gasZ081ZG", true, -38, 1]], [104, "black", 0, 6, 3, [[41, 0, -39, [4]]], [0, "02E4I78oBApIBwDrMRtSQy", 2], [4, 4278190080], [5, 800, 1800], [0, 664.993, 0, 0, 0, 0, 1, 0.95238, 0.95238, 0.95238]], [48, "btn_jump", 3, [7], [0, "092jbYZAdFzKZWQ+pYcWTF", 2]], [1, "Main Camera", 1, [-40], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 1, 7, -1, 12], [6, "BgCamera", 1, [[3, 16, 2, 10, -41]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "BigMapCamera", 1, [-42], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 512, 2, 15, 15], [1, "GameCamera", 1, [-43], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 4, 2, 20, 17], [1, "Game2Camera", 1, [-44], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 32, 2, 30, 19], [1, "UICamera", 1, [-45], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 2, 2, 40, 21], [1, "EffectCamera", 1, [-46], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 128, 2, 50, 23], [1, "CurtainCamera", 1, [-47], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 64, 2, 60, 25], [6, "TopCamera", 1, [[3, 256, 2, 70, -48]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [11, "CameraFollow", 1, [[44, -49, 16]]], [11, "Graphics", 1, [[45, 1, -50, [7], [4, 4279506156]]]], [36, "COLLISION_MANAGER_DEBUG_DRAW", "67YjB8ItlFVKkILU75MN1z", 5, [[46, -51, [8]]]]], 0, [0, 0, 1, 0, 0, 1, 0, 53, 4, 0, 9, 26, 0, 10, 24, 0, 11, 20, 0, 12, 18, 0, 13, 22, 0, 14, 13, 0, 0, 1, 0, -1, 2, 0, -2, 12, 0, -3, 14, 0, -4, 15, 0, -5, 17, 0, -6, 19, 0, -7, 21, 0, -8, 23, 0, -9, 25, 0, -10, 27, 0, -11, 28, 0, 2, 1, 0, -13, 29, 0, 3, 2, 0, -1, 3, 0, 0, 3, 0, -1, 6, 0, -2, 10, 0, -3, 11, 0, 0, 4, 0, -1, 8, 0, -2, 9, 0, -2, 30, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 3, 8, 0, 3, 9, 0, 0, 10, 0, -1, 13, 0, 0, 14, 0, -1, 16, 0, -1, 18, 0, -1, 20, 0, -1, 22, 0, -1, 24, 0, -1, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 5, 5, 1, 2, 5, 4, 2, 6, 7, 2, 11, 51], [0, 0, 0, 0, 0, 0, 0, 0, 0], [6, 6, 4, -1, -1, -1, 1, -1, -1], [47, 48, 22, 22, 0, 0, 49, 18, 19]], [[{"name": "btn_block", "rect": [117, 848, 105, 96], "offset": [0, 0], "originalSize": [105, 96], "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [7], [20]], [[{"name": "reddot1", "rect": [274, 717, 39, 40], "offset": [0, 0], "originalSize": [39, 40], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [7], [20]], [[{"name": "jump", "rect": [0, 0, 148, 71], "offset": [0, 0], "originalSize": [148, 71], "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [7], [50]], [[[164, "scene1_0in"]], 0, 0, [], [], []], [[[165, "anim_btn_idle", 1.3333333333333333, 2, {"props": {"y": [{"frame": 0, "value": 0, "curve": "quadInOut"}, {"frame": 0.6666666666666666, "value": 10, "curve": "quadInOut"}, {"frame": 1.3333333333333333, "value": 0}]}}]], 0, 0, [], [], []], [[[166, "building_furniture_anim_preview"], [105, "building_furniture_anim_preview", [-5, -6, -7], [[148, -2, [[32, 33, null], 6, 6, 0]], [167, -3], [168, -4]], [142, -1, 0], [-3.295, 88.948, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "cloud", 1, [-8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19], [4, "882qwr+RJE/7ZLQLCfWEr5", 1, 0], [5, 600, 600], [0, 0, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [37, "hammer", 1, [-20, -21], [4, "d3IxY+ImVCpLKDW5ZVFK/j", 1, 0], [5, 200, 200], [0, 0, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [37, "broom", 1, [-22, -23], [4, "25WP5WtiBMObt2067a9NkD", 1, 0], [5, 200, 200], [0, 0, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [19, "1", 2, [[2, -24, [0], 1]], [4, "12003IFUBFAZqoSVk2VZa/", 1, 0], [5, 574, 586]], [9, "2", 2, [[2, -25, [2], 3]], [4, "9cRoVohthOmYz1cPA9Oeqk", 1, 0], [5, 574, 586], [10.579, 30.711, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "3", 2, [[2, -26, [4], 5]], [4, "fdIGaox0hMR7YHhCPBAODK", 1, 0], [5, 574, 586]], [9, "4", 2, [[2, -27, [6], 7]], [4, "57JOito/lKm7hw2g7LRGt9", 1, 0], [5, 574, 586], [-75.234, -48.058, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "5", 2, [[2, -28, [8], 9]], [4, "c0XFaCQZNAzo/IiI8hUoCk", 1, 0], [5, 574, 586], [-67.175, 42.175, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "6", 2, [[2, -29, [10], 11]], [4, "8eBF0sdUxHapIMG+Fe3kEq", 1, 0], [5, 574, 586], [21.931, 30.047, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "7", 2, [[2, -30, [12], 13]], [4, "e7HoRNNGJI5p4SrMD4Cher", 1, 0], [5, 574, 586], [0, 11.158, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "8", 2, [[2, -31, [14], 15]], [4, "4erv7jjq1Pnp8euvFfv50J", 1, 0], [5, 574, 586], [-45.816, -61.71, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "9", 2, [[2, -32, [16], 17]], [4, "22CEmv901MuI1xbdQW2Ovm", 1, 0], [5, 574, 586], [45.162, -61.369, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "10", 2, [[2, -33, [18], 19]], [4, "aeHkS7FHpBR49QWNCXldMs", 1, 0], [5, 574, 586]], [19, "11", 2, [[2, -34, [20], 21]], [4, "3fMwH/Be9HEoL+KzuWDKg3", 1, 0], [5, 574, 586]], [19, "12", 2, [[2, -35, [22], 23]], [4, "45ZFOxeq5FLputwzltyTDc", 1, 0], [5, 574, 586]], [12, "hammer01", 3, [[2, -36, [24], 25]], [4, "89XQHhWiZOra8syx77rZPU", 1, 0], [5, 147, 168], [0, 0.7, 0.3], [116, 132, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "hammer02", 3, [[2, -37, [26], 27]], [4, "1eOdGQ3a5PvrBPicyMOEjW", 1, 0], [5, 141, 181], [-99, 95, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "broom02", 4, [[2, -38, [28], 29]], [4, "c3Ld4O6QFLuY24ydV4HwjI", 1, 0], [5, 154, 170], [0, 0.2, 0.8], [-101, 64, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "broom01", 4, [[2, -39, [30], 31]], [4, "f3meCNXbFKPLDAViODZ84N", 1, 0], [5, 152, 209], [0, 0.7, 0.8], [96, 70, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 11, 0, -8, 12, 0, -9, 13, 0, -10, 14, 0, -11, 15, 0, -12, 16, 0, -1, 17, 0, -2, 18, 0, -1, 19, 0, -2, 20, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 54, 1, 39], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -2], [0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 51, 0, 52, 0, 53, 0, 54, 55, 56]], [[[18, "HomeScene", null], [49, "<PERSON><PERSON><PERSON>", 6, "5fWRgfcKVMeahie/uQUTgm", [-7, -8, -9, -10, -11, -12, -13], [[78, -6, -5, -4, -3, -2]], [27, -1], [5, 750, 1634]], [66, "<PERSON><PERSON>", "377SojyLdO4ZDj0O4BZ5dq", [[-38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53, [15, "FlyNode", -54]], 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], [[28, true, false, -14, [5, 750, 1334]], [79, -24, -23, -22, -21, -20, -19, -18, -17, -16, -15, 56], [8, 45, -25], [80, -37, [-26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36]]], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [50, "UI", 2, [-62, -63, -64, -65, -66, -67, -68, -69], [[8, 5, -55], [81, -61, -60, -59, -58, -57, -56]], [5, 750, 1334]], [63, "MapLayer", 9, [[[15, "empty", -71], -72, -73, -74, -75], 4, 1, 1, 1, 1], [-70], [0, 0, 0]], [134, "Bg", 9, 4, [-77, -78, -79, -80, -81], [-76], [4, 4278190080], [5, 750, 12800], [0, 0, 0], [380, 677, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "btn", [-86, -87], [[75, 3, -83, [[31, "e2367Hbp1VLSIfFm0zCcMra", "onRabbitPopWin", -82]]], [17, true, -84, [32], 31], [82, -85]], [5, 150, 150], [0, 0.5, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [106, "btn_rabbit", 9, [6, -90], [[83, -89, [-88]]], [5, 120, 120], [0, 0.5, 0], [6500, 6050, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "bg", 6, [-92, -93, -94], [[21, -91, [75, 76], 74]], [0, "38fovRCmtPB7RdJpWRHhAj", 1], [5, 620, 190]], [150, "New Node", false, true, [2, 1, -95], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 512, 0, 15], [62, "reddot", 9, [-98], [[-96, [17, true, -97, [30], 29]], 1, 4], [5, 39, 40], [0, 0.5, 0]], [23, "num", false, 9, 11, [-102], [[32, 1, 1, -6, -99, [5, 51, 80]], [33, -3, -101, -100, [18, 19, 20, 21, 22, 23, 24, 25, 26, 27]]], [5, 51, 80], [0, 32.047, 0, 0, 0, 0, 1, 0.45, 0.45, 0.6]], [53, "reddot", 9, [-105], [[2, -103, [46], 47], [17, true, -104, [49], 48]], [5, 39, 40], [0, 0.5, 0]], [23, "num", false, 9, 13, [-109], [[32, 1, 1, -6, -106, [5, 51, 80]], [33, -3, -108, -107, [36, 37, 38, 39, 40, 41, 42, 43, 44, 45]]], [5, 51, 80], [0, 32.047, 0, 0, 0, 0, 1, 0.45, 0.45, 0.6]], [11, "BigMapNode", 3, [[84, -110, 4], [169, -111, 4, 5, 50]]], [38, "WrapBlack", 1, 3, [-113], [[8, 5, -112]], [5, 750, 1334]], [54, "Render", 2, [-117, -118], [[85, -116, -115, -114, 54, 55]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [24, "arrow", 6, 1, [-120], [[21, -119, [63, 64, 65, 66], 62]], [0, "9701zhLFdEBLJtPru/ur62", 1]], [24, "finger", 6, 1, [-123], [[86, -122, -121]], [0, "86T366eXFJk7II2lQz5L8a", 1]], [7, 1, 1, -1], [7, 4, 0, 20], [7, 32, 0, 30], [7, 2, 0, 40], [7, 128, 0, 50], [7, 64, 0, 60], [107, "Center", 0, 3, [[8, 45, -124]], [5, 750, 1334]], [108, "BigMapStage", 9, 3, [4], [[87, -128, -127, 10, -126, -125]], [0, 0, 0], [-8960, -6400, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "ground", false, 9, 4, [[5, 1, 0, -129, [0], 1]], [4, 4282626182], [5, 17920, 13312], [0, 0, 0]], [109, "bubble2", 9, [-131], [[2, -130, [15], 16]], [5, 140, 140]], [67, "reddot", 9, 6, [11], [-132], [5, 50, 50], [0, 0.5, 0], [54.194, 92, 0, 0, 0, 0, 1, 1, 1, 1]], [110, "guide_light", 180, 7, [[145, false, 0, -133, [33], 34], [82, -134]], [4, 4289769719], [5, 180, 180], [1.81, 59.377, 0, 0, 0, 0, 1, 1, 1, 1]], [111, "ViewPoint", false, 0, 4, [13], [[162, -135, [[31, "d9691X4pixJ1LsuD1QS7MHL", "onPrintPoint", 4]]]]], [14, "WrapTop", 1, 3, [[8, 1, -136]], [5, 750, 80], [0, 627, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "WrapBottom", 1, 3, [[40, 4, 60, -137]], [5, 750, 140], [0, -537, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "HomeBlack", false, 16, [70, true, -138, 51]], [55, "MapMask", 1, [-139, -140], [0, "0fNmBv+sNL/akenV5dsQWW", 1], [5, 750, 1624]], [22, "bg_mask", 6, 1, [-142], [[88, true, -141, [59]]], [0, "e6tvl8aeJB7pg7v5IVArvi", 1], [5, 199, 100], [0, 225.086, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "blackbg", 6, 37, [[20, 0, -143, [57], 58]], [0, "0cuW0iqm1Dz5mtiVVDeAmt", 1], [4, 4278190080], [5, 1600, 4000]], [34, "singletxt", 6, 1, [8], [0, "0cX2fpHJJDiYAMkZY/gU71", 1], [5, 750, 230], [0, 468, 0, 0, 0, 0, 1, 1, 1, 1]], [112, "bubble", 8, [-145], [[5, 1, 0, -144, [68], 69]], [0, "80OnZu+01AbZwb4CMJ6GRY", 1], [5, 620, 190], [0, 0, 0], [-281.738, -92.053, 0, 0, 0, 0, 1, 1, 1, 1]], [56, "richtext", 6, 40, [[89, "<color=#6C4124>描述描述。描述描述描述</c><color=#c16746>特殊文字</color>描", 1, 30, 378, 42, -147]], [0, "06PrNr5PZKopv6cAUDnOm1", -146], [4, 4280566124], [5, 378, 94.91999999999999], [356.027, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "btn_close", false, 6, 8, [-149], [[76, 3, -148]], [0, "81k6tTHiRFaaU1/ATW+3T+", 1], [5, 60, 60], [248.513, 63.597, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "Main Camera", 2, [20], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "BgCamera", 2, [-150], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 16, 0, 10, 44], [1, "BigMapCamera", 2, [10], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "GameCamera", 2, [21], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "Game2Camera", 2, [22], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "UICamera", 2, [23], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [132, "PushBallCamera", false, 2, [-151], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 32, 0, 41, 50], [1, "EffectCamera", 2, [24], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "CurtainCamera", 2, [25], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "TopCamera", 2, [-152], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 256, 0, 70, 54], [11, "CameraFollow", 2, [[44, -153, 10]]], [15, "ConvertNode", 2], [113, "ThreeDCameraParent", 2, [-154]], [133, "ThreeDCamera", true, 58, [-155], [0, 0, 500, 0, 0, 0, 1, 1, 1, 1]], [157, 1024, 43, 1.5, 105, 1024, false, false, 59], [13, "left", false, 9, 5, [[5, 1, 0, -156, [2], 3]], [4, 4279571949], [5, 50, 12700], [0, 0, 0], [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "right", false, 9, 5, [[5, 1, 0, -157, [4], 5]], [4, 4292942597], [5, 50, 12700], [0, 0, 0], [17870, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "top", false, 9, 5, [[5, 1, 0, -158, [6], 7]], [4, 4292942597], [5, 17920, 50], [0, 0, 0], [0, 12750, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "bottom", false, 9, 5, [[5, 1, 0, -159, [8], 9]], [4, 4292942597], [5, 17920, 50], [0, 0, 0]], [13, "center", false, 2, 5, [[5, 1, 0, -160, [10], 11]], [4, 4292942597], [5, 50, 50], [0, 0, 0], [8935, 6375, 0, 0, 0, 0, 1, 1, 1, 1]], [42, 1, 0, 5, [12]], [114, "BgBtn", 0, 9, 4, [7], [0, 0, 0]], [115, "store_bg", 9, 6, [29], [5, 130, 130], [0, 60, 0, 0, 0, 0, 1, 1, 1, 0]], [14, "btn_block", 9, 29, [[2, -161, [13], 14]], [5, 105, 96], [-4.649, 1.162, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "sprite", 9, 12, [-162], [5, 51, 65]], [30, false, 70, [17]], [29, 11, [28]], [90, 30, 72], [16, "sprite", 9, 14, [-163], [5, 51, 65]], [30, false, 74, [35]], [91, 4, 66], [14, "WrapCardTip", 1, 3, [[171, -164]], [5, 227, 324], [256, -400, 0, 0, 0, 0, 1, 1, 1, 1]], [58, "WrapBuildBottom", 1, 3, [5, 227, 324], [1.336, -796.144, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "Graphics", 2, [[45, 1, -165, [52], [4, 4279506156]]]], [64, "RenderTextureCam", 17, [-166]], [73, false, 1014, 4, 70, 80], [16, "New Sprite", 4, 17, [-167], [5, 1000, 2000]], [41, 0, 82, [53]], [26, "mask", false, 6, 1, [[10, -168]], [0, "6ek5rJyO1IC7ZYSK3Nomgb", 1], [5, 750, 1634]], [12, "Top", 36, [[10, -169]], [0, "ed2h70P1hEyq2EK/073Y45", 1], [5, 750, 500], [0, 0.5, 0], [0, 410.848, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "Bottom", 36, [[10, -170]], [0, "f7lwgM2RVAjYQc7mAwFEsc", 1], [5, 750, 500], [0, 0.5, 1], [0, -509.341, 0, 0, 0, 0, 1, 1, 1, 1]], [59, "narrow", 6, 18, [[20, 2, -171, [60], 61]], [0, "997hFdKSNDdLiqiwDBWdK5", 1], [5, 119, 141], [0, 0.5, 0]], [65, "hand", 19, [-172], [0, "07B2/RxVxNnLVnQLHGJLLL", 1], [5, 282.7, 294.32], [72, -98, 0, 0, 0, 0, 1, 1, 1, 1]], [92, "armatureName", 0, "e3bcf7f8-37c0-4a58-bcbb-ea331913f6ba#be01fec8-2a47-45c1-967f-0d8b199b77a0", 88, [67]], [116, "npc_1", 8, [-173], [0, "bbP/B6VZxDxr+DijfpBom3", 1], [5, 236, 360], [0, 0.4, 0], [-310.252, -81.752, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "npc_1", 90, [[146, 2, false, -174, [70], 71]], [0, "b37sxpJKhPHpwK/RToTYFS", 1], [5, 245, 304], [0, 0.5, 0], [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "btn_close", 6, 42, [[2, -175, [72], 73]], [0, "05ZrPDZdNCKLbS5vuu997n", 1], [4, 4283994034], [5, 24, 24]], [26, "btn", false, 6, 1, [[77, -176]], [0, "c32Nanfa1BeqYGK+oFiWN3", 1], [5, 123, 165]], [36, "COLLISION_MANAGER_DEBUG_DRAW", "67YjB8ItlFVKkILU75MN1z", 9, [[46, -177, [77]]]]], 0, [0, 3, 1, 0, 16, 39, 0, 17, 19, 0, 18, 18, 0, 19, 38, 0, 0, 1, 0, -1, 84, 0, -2, 36, 0, -3, 37, 0, -4, 18, 0, -5, 19, 0, -6, 39, 0, -7, 93, 0, 0, 2, 0, 9, 25, 0, 10, 24, 0, 11, 22, 0, 12, 21, 0, 13, 23, 0, 14, 20, 0, 20, 35, 0, 21, 15, 0, 22, 3, 0, 0, 2, 0, 0, 2, 0, -1, 20, 0, -2, 45, 0, -3, 10, 0, -4, 21, 0, -5, 22, 0, -6, 23, 0, -7, 24, 0, -8, 25, 0, -9, 55, 0, -10, 60, 0, -11, 51, 0, 0, 2, 0, -1, 43, 0, -2, 44, 0, -3, 46, 0, -4, 47, 0, -5, 48, 0, -6, 49, 0, -7, 50, 0, -8, 52, 0, -9, 53, 0, -10, 54, 0, -11, 56, 0, -12, 57, 0, -13, 58, 0, -14, 3, 0, -15, 79, 0, -16, 17, 0, 2, 2, 0, 0, 3, 0, 23, 78, 0, 24, 34, 0, 25, 26, 0, 26, 16, 0, 27, 33, 0, 0, 3, 0, -1, 26, 0, -2, 27, 0, -3, 15, 0, -4, 33, 0, -5, 34, 0, -6, 16, 0, -7, 77, 0, -8, 78, 0, -1, 76, 0, 2, 4, 0, -2, 28, 0, -3, 5, 0, -4, 67, 0, -5, 32, 0, -1, 66, 0, -1, 61, 0, -2, 62, 0, -3, 63, 0, -4, 64, 0, -5, 65, 0, 28, 7, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 68, 0, -2, 30, 0, -1, 73, 0, 0, 7, 0, -2, 31, 0, 0, 8, 0, -1, 40, 0, -2, 90, 0, -3, 42, 0, -3, 94, 0, -1, 72, 0, 0, 11, 0, -1, 12, 0, 0, 12, 0, 8, 71, 0, 0, 12, 0, -1, 70, 0, 0, 13, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, 8, 75, 0, 0, 14, 0, -1, 74, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, -1, 35, 0, 29, 83, 0, 30, 81, 0, 0, 17, 0, -1, 80, 0, -2, 82, 0, 0, 18, 0, -1, 87, 0, 31, 89, 0, 0, 19, 0, -1, 88, 0, 0, 26, 0, 32, 76, 0, 33, 57, 0, 34, 28, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, -1, 69, 0, -1, 73, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, 3, 35, 0, -1, 85, 0, -2, 86, 0, 0, 37, 0, -1, 38, 0, 0, 38, 0, 0, 40, 0, -1, 41, 0, 3, 41, 0, 0, 41, 0, 0, 42, 0, -1, 92, 0, -1, 45, 0, -1, 51, 0, -1, 55, 0, 0, 56, 0, -1, 59, 0, -1, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 69, 0, -1, 71, 0, -1, 75, 0, 0, 77, 0, 0, 79, 0, -1, 81, 0, -1, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, -1, 89, 0, -1, 91, 0, 0, 91, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 5, 9, 1, 2, 9, 2, 2, 9, 4, 2, 27, 6, 2, 7, 7, 2, 67, 8, 2, 39, 10, 0, 46, 11, 2, 30, 13, 2, 32, 20, 0, 43, 21, 0, 47, 22, 0, 48, 23, 0, 49, 24, 0, 52, 25, 0, 53, 29, 2, 68, 177], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 66, 71, 72, 75, 89, 89], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -1, 4, -1, 4, -1, -1, 1, -1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -1, 1, 4, -1, 35, 6, -1, -1, 36, 37, 38, -1, 1, -1, -1, 1, 4, -1, -2, -3, -4, -1, -1, 1, -1, 1, -1, 1, 4, -1, -2, -1, 1, 1, 1, 1, 39, 40], [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 23, 0, 24, 0, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 0, 4, 4, 14, 14, 0, 57, 0, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 0, 15, 4, 4, 17, 25, 18, 26, 27, 28, 1, 0, 29, 0, 0, 30, 16, 31, 32, 33, 16, 0, 0, 58, 0, 59, 0, 34, 35, 35, 60, 19, 1, 3, 15, 3, 36, 37]], [["7@34_0,9729,9729,33071,33071,0,0,1", -1], [20], 0, [], [], []], [[{"name": "bubble2", "rect": [2, 349, 140, 140], "offset": [0, 0], "originalSize": [140, 140], "capInsets": [53, 22, 22, 34]}], [6], 0, [0], [7], [20]], [[[18, "GameScene", null], [129, "<PERSON><PERSON>", "c4yrwaYLNCzrciPLUPaeel", [-3, -4, -5, -6, -7, -8, -9, -10], [[[28, true, false, -1, [5, 750, 1334]], [8, 45, -2], null], 4, 4, 0], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "New Node", false, [1], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Main Camera", 1, [[72, 7, -1, -11]], [0, 0, 642.5908496080535, 0, 0, 0, 1, 1, 1, 1]], [6, "BgCamera", 1, [[3, 16, 2, 10, -12]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [6, "GameCamera", 1, [[3, 4, 2, 20, -13]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [6, "Game2Camera", 1, [[3, 32, 2, 30, -14]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [6, "UICamera", 1, [[3, 2, 2, 40, -15]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [6, "EffectCamera", 1, [[3, 128, 2, 50, -16]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [6, "CurtainCamera", 1, [[3, 64, 2, 60, -17]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [6, "TopCamera", 1, [[3, 256, 2, 70, -18]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, -7, 9, 0, -8, 10, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 5, 2, 1, 2, 2, 18], [], [], []], [[[18, "HomeIpadScene", null], [49, "<PERSON><PERSON><PERSON>", 6, "5fWRgfcKVMeahie/uQUTgm", [-7, -8, -9, -10, -11, -12, -13], [[78, -6, -5, -4, -3, -2]], [27, -1], [5, 750, 1634]], [47, "<PERSON><PERSON>", "377SojyLdO4ZDj0O4BZ5dq", [-36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50], [[143, -14, [5, 750, 1334]], [79, -24, -23, -22, -21, -20, -19, -18, -17, -16, -15, 55], [8, 45, -25], [80, -35, [-26, -27, -28, -29, -30, -31, -32, -33, -34]]], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [50, "UI", 2, [-58, -59, -60, -61, -62, -63], [[68, 45, 750, -51], [81, -57, -56, -55, -54, -53, -52]], [5, 750, 1334]], [63, "MapLayer", 9, [[[15, "empty", -65], -66, -67, -68, -69], 4, 1, 1, 1, 1], [-64], [0, 0, 0]], [135, "Bg", 9, 4, [-71, -72, -73, -74, -75], [-70], [5, 750, 1334], [0, 0, 0]], [151, "New Node", false, [[2, [117, "FlyNode", "dfuld1x/xIRr2Z3f+U/JYU", -76, [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], 1, -77], 1, 4, 1, 1], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "btn", [-81, -82], [[75, 3, -79, [[31, "e2367Hbp1VLSIfFm0zCcMra", "onRabbitPopWin", -78]]], [17, true, -80, [32], 31]], [5, 150, 150], [0, 0.5, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [52, "bg", 6, [-84, -85, -86], [[5, 1, 0, -83, [70], 71]], [0, "38fovRCmtPB7RdJpWRHhAj", 1], [5, 560, 180]], [7, 512, 0, 15], [118, "btn_rabbit", false, 9, [7], [[83, -88, [-87]]], [5, 120, 120], [0, 0.5, 0], [6500, 6050, 0, 0, 0, 0, 1, 1, 1, 1]], [62, "reddot", 9, [-91], [[-89, [17, true, -90, [30], 29]], 1, 4], [5, 39, 40], [0, 0.5, 0]], [23, "num", false, 9, 11, [-95], [[32, 1, 1, -6, -92, [5, 51, 80]], [33, -3, -94, -93, [18, 19, 20, 21, 22, 23, 24, 25, 26, 27]]], [5, 51, 80], [0, 32.047, 0, 0, 0, 0, 1, 0.45, 0.45, 0.6]], [53, "reddot", 9, [-98], [[2, -96, [44], 45], [17, true, -97, [47], 46]], [5, 39, 40], [0, 0.5, 0]], [23, "num", false, 9, 13, [-102], [[32, 1, 1, -6, -99, [5, 51, 80]], [33, -3, -101, -100, [34, 35, 36, 37, 38, 39, 40, 41, 42, 43]]], [5, 51, 80], [0, 32.047, 0, 0, 0, 0, 1, 0.45, 0.45, 0.6]], [11, "BigMapNode", 3, [[170, -103, 4, 48, 49], [84, -104, 4]]], [38, "WrapBlack", 1, 3, [-106], [[8, 5, -105]], [5, 750, 1334]], [54, "Render", 2, [-110, -111], [[85, -109, -108, -107, 53, 54]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [24, "arrow", 6, 1, [-113], [[21, -112, [62, 63, 64, 65], 61]], [0, "9701zhLFdEBLJtPru/ur62", 1]], [24, "finger", 6, 1, [-116], [[86, -115, -114]], [0, "f6V2zG76RGe4337xKwsOLi", 1]], [7, 1, 1, -1], [7, 4, 0, 20], [7, 32, 0, 30], [7, 2, 0, 40], [7, 128, 0, 50], [7, 64, 0, 60], [119, "Center", 3, [[8, 45, -117]], [5, 750, 1334]], [120, "BigMapStage", 9, 3, [4], [[87, -121, -120, 9, -119, -118]], [0, 0, 0]], [121, "ground", 9, 4, [[5, 1, 0, -122, [0], 1]], [4, 4282626182], [5, 750, 1334], [0, 0, 0], [-1218.862, -2401.875, 0, 0, 0, 0, 1, 1, 1, 1]], [122, "store_bg", 9, 7, [-124], [[74, 1.1, 3, -123, [[163, "45978sF+O5OuIB2wdfnCWHA", "onRoleDress"]]]], [5, 130, 130], [0, 60, 0, 0, 0, 0, 1, 1, 1, 0]], [38, "bubble2", 9, 29, [-126], [[2, -125, [15], 16]], [5, 140, 140]], [67, "reddot", 9, 7, [11], [-127], [5, 50, 50], [0, 0.5, 0], [54.194, 92, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "WrapTop", 1, 3, [[68, 40, 750, -128]], [5, 750, 125], [0, 627, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "WrapBottom", 1, 3, [[40, 4, 60, -129]], [5, 750, 140], [0, -537, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "HomeBlack", false, 16, [70, true, -130, 50]], [55, "MapMask", 1, [-131, -132], [0, "0fNmBv+sNL/akenV5dsQWW", 1], [5, 750, 1624]], [22, "bg_mask", 6, 1, [-134], [[88, true, -133, [58]]], [0, "e6tvl8aeJB7pg7v5IVArvi", 1], [5, 199, 100], [0, 225.086, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "blackbg", 6, 36, [[20, 0, -135, [56], 57]], [0, "0cuW0iqm1Dz5mtiVVDeAmt", 1], [4, 4278190080], [5, 1600, 4000]], [34, "singletxt", 6, 1, [8], [0, "0cX2fpHJJDiYAMkZY/gU71", 1], [5, 750, 230], [0, 468, 0, 0, 0, 0, 1, 1, 1, 1]], [56, "richtext", 6, 8, [[89, "<color=#4D4943>描述描述描述描述描描述描,述描述</c><color=#c16746>特殊文字</color>", 1, 30, 340, 42, -137]], [0, "99boX8OHFEJoF+gwlfZ3RT", -136], [4, 4282599757], [5, 340, 94.91999999999999], [56.216, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "btn_close", false, 6, 8, [-139], [[76, 3, -138]], [0, "81k6tTHiRFaaU1/ATW+3T+", 1], [5, 60, 60], [248.513, 63.597, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "Main Camera", 2, [20], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "BgCamera", 2, [-140], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 16, 0, 10, 42], [1, "BigMapCamera", 2, [9], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "GameCamera", 2, [21], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "Game2Camera", 2, [22], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "UICamera", 2, [23], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "EffectCamera", 2, [24], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "CurtainCamera", 2, [25], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [1, "TopCamera", 2, [-141], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [3, 256, 0, 70, 50], [11, "CameraFollow", 2, [[44, -142, 9]]], [15, "ConvertNode", 2], [13, "left", false, 9, 5, [[5, 1, 0, -143, [2], 3]], [4, 4279571949], [5, 50, 12700], [0, 0, 0], [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "right", false, 9, 5, [[5, 1, 0, -144, [4], 5]], [4, 4292942597], [5, 50, 12700], [0, 0, 0], [17870, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "top", false, 9, 5, [[5, 1, 0, -145, [6], 7]], [4, 4292942597], [5, 17920, 50], [0, 0, 0], [0, 12750, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "bottom", false, 9, 5, [[5, 1, 0, -146, [8], 9]], [4, 4292942597], [5, 17920, 50], [0, 0, 0]], [13, "center", false, 2, 5, [[5, 1, 0, -147, [10], 11]], [4, 4292942597], [5, 50, 50], [0, 0, 0], [8935, 6375, 0, 0, 0, 0, 1, 1, 1, 1]], [42, 1, 0, 5, [12]], [123, "BgBtn", 9, 4, [10], [0, 0, 0]], [14, "btn_block", 9, 30, [[2, -148, [13], 14]], [5, 105, 96], [-4.649, 1.162, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "sprite", 9, 12, [-149], [5, 51, 65]], [30, false, 62, [17]], [29, 11, [28]], [90, 31, 64], [124, "ViewPoint", false, 4, [13]], [16, "sprite", 9, 14, [-150], [5, 51, 65]], [30, false, 67, [33]], [91, 4, 59], [58, "WrapBuildBottom", 1, 2, [5, 227, 324], [1.336, -623.781, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "Graphics", 2, [[45, 1, -151, [51], [4, 4279506156]]]], [64, "RenderTextureCam", 17, [-152]], [73, false, 1014, 1, 70, 72], [16, "New Sprite", 4, 17, [-153], [5, 1000, 2000]], [147, 1, 0, 0, 74, [52]], [26, "mask", false, 6, 1, [[10, -154]], [0, "6ek5rJyO1IC7ZYSK3Nomgb", 1], [5, 750, 1634]], [12, "Top", 35, [[10, -155]], [0, "ed2h70P1hEyq2EK/073Y45", 1], [5, 750, 500], [0, 0.5, 0], [0, 410.848, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "Bottom", 35, [[10, -156]], [0, "f7lwgM2RVAjYQc7mAwFEsc", 1], [5, 750, 500], [0, 0.5, 1], [0, -509.341, 0, 0, 0, 0, 1, 1, 1, 1]], [59, "narrow", 6, 18, [[20, 2, -157, [59], 60]], [0, "997hFdKSNDdLiqiwDBWdK5", 1], [5, 119, 141], [0, 0.5, 0]], [65, "hand", 19, [-158], [0, "53wV6hs49IloHxIK5WcQTQ", 1], [5, 282.7, 294.32], [72, -98, 0, 0, 0, 0, 1, 1, 1, 1]], [92, "armatureName", 0, "e3bcf7f8-37c0-4a58-bcbb-ea331913f6ba#be01fec8-2a47-45c1-967f-0d8b199b77a0", 80, [66]], [125, "npc_1", 6, 8, [[29, -159, [67]]], [0, "bfAh84tp1MK7hqe5hix7ef", 1], [5, 198, 248], [0, 0.5, 0], [-195.11, -83.409, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "btn_close", 6, 40, [[2, -160, [68], 69]], [0, "05ZrPDZdNCKLbS5vuu997n", 1], [4, 4283994034], [5, 24, 24]], [26, "btn", false, 6, 1, [[77, -161]], [0, "c32Nanfa1BeqYGK+oFiWN3", 1], [5, 123, 165]], [36, "COLLISION_MANAGER_DEBUG_DRAW", "67YjB8ItlFVKkILU75MN1z", 6, [[46, -162, [72]]]]], 0, [0, 3, 1, 0, 16, 38, 0, 17, 19, 0, 18, 18, 0, 19, 37, 0, 0, 1, 0, -1, 76, 0, -2, 35, 0, -3, 36, 0, -4, 18, 0, -5, 19, 0, -6, 38, 0, -7, 84, 0, 0, 2, 0, 9, 25, 0, 10, 24, 0, 11, 22, 0, 12, 21, 0, 13, 23, 0, 14, 20, 0, 20, 34, 0, 21, 15, 0, 22, 3, 0, 0, 2, 0, 0, 2, 0, -1, 20, 0, -2, 43, 0, -3, 9, 0, -4, 21, 0, -5, 22, 0, -6, 23, 0, -7, 24, 0, -8, 25, 0, -9, 51, 0, 0, 2, 0, -1, 41, 0, -2, 42, 0, -3, 44, 0, -4, 45, 0, -5, 46, 0, -6, 47, 0, -7, 48, 0, -8, 49, 0, -9, 50, 0, -10, 52, 0, -11, 53, 0, -12, 3, 0, -13, 71, 0, -14, 70, 0, -15, 17, 0, 0, 3, 0, 23, 70, 0, 24, 33, 0, 25, 26, 0, 26, 16, 0, 27, 32, 0, 0, 3, 0, -1, 26, 0, -2, 27, 0, -3, 15, 0, -4, 32, 0, -5, 33, 0, -6, 16, 0, -1, 69, 0, 2, 4, 0, -2, 28, 0, -3, 5, 0, -4, 60, 0, -5, 66, 0, -1, 59, 0, -1, 54, 0, -2, 55, 0, -3, 56, 0, -4, 57, 0, -5, 58, 0, 2, 6, 0, -4, 85, 0, 28, 10, 0, 0, 7, 0, 0, 7, 0, -1, 29, 0, -2, 31, 0, 0, 8, 0, -1, 39, 0, -2, 82, 0, -3, 40, 0, -1, 65, 0, 0, 10, 0, -1, 64, 0, 0, 11, 0, -1, 12, 0, 0, 12, 0, 8, 63, 0, 0, 12, 0, -1, 62, 0, 0, 13, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, 8, 68, 0, 0, 14, 0, -1, 67, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, -1, 34, 0, 29, 75, 0, 30, 73, 0, 0, 17, 0, -1, 72, 0, -2, 74, 0, 0, 18, 0, -1, 79, 0, 31, 81, 0, 0, 19, 0, -1, 80, 0, 0, 26, 0, 32, 69, 0, 33, 53, 0, 34, 28, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, -1, 30, 0, 0, 30, 0, -1, 61, 0, -1, 65, 0, 0, 32, 0, 0, 33, 0, 3, 34, 0, -1, 77, 0, -2, 78, 0, 0, 36, 0, -1, 37, 0, 0, 37, 0, 3, 39, 0, 0, 39, 0, 0, 40, 0, -1, 83, 0, -1, 43, 0, -1, 51, 0, 0, 52, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 61, 0, -1, 63, 0, -1, 68, 0, 0, 71, 0, -1, 73, 0, -1, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, 0, 79, 0, -1, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 5, 6, 1, 2, 6, 2, 2, 6, 4, 2, 27, 7, 2, 10, 8, 2, 38, 9, 0, 44, 10, 2, 60, 11, 2, 31, 13, 2, 66, 20, 0, 41, 21, 0, 45, 22, 0, 46, 23, 0, 47, 24, 0, 48, 25, 0, 49, 162], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63, 64, 68, 81, 81], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -1, 4, -1, 4, -1, -1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -1, 1, 4, -1, 35, 55, 6, -1, -1, 36, 37, 38, -1, 1, -1, -1, 1, 4, -1, -2, -3, -4, -1, -1, -1, 1, -1, 1, -1, 1, 1, 1, 39, 40], [0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 23, 0, 24, 0, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 0, 4, 4, 14, 14, 0, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 0, 15, 4, 4, 17, 61, 25, 18, 26, 27, 28, 1, 0, 29, 0, 0, 30, 16, 31, 32, 33, 16, 0, 0, 0, 34, 0, 62, 19, 3, 15, 3, 36, 37]]]]