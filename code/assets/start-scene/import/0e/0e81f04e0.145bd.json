[1, ["ecpdLyjvZBwrvm+cedCcQy", "1e7f1f20a", "1f0b2a47c", "87TYtviyVD+776A8cads6m", "2fM4cHzEJJRKBcflDVGeCC", "0dCiFbYGpDJ6bCjkyhPiaS", "98O03su+JNMLlMEjtaiR4Y", "8baFkYGY9Hd45EduxPpVGf", "1546d77af", "9crPdGvoxMX6sVcW8Enj6U", "77QmE5hTxMC63gWEBSsDDQ", "bbK+biqFlNopAzduf/K25Z", "791Z2doL9PNqEJbni+exWI", "deZuoyP8JBk5Eb1EHoOYJS", "28rZ/hjSFHhKqeVlEG+O59", "1bOGf1RBFHcL7owSnIxTaD", "5eW/fnujlKera2yGqW6wkO", "25gUg4VkhAAbERC7NJ4ng6", "58jWYrnslCxZ8KO8OzlkPX", "dff+aQjBFNtb337VARX2rv", "1cBwmn2OhOIq0yQhMBzHtA", "c4BP0QtQdLNpMYWbI77iEV", "e3/5GailtK+5zX+tQlsfu8", "70eond94VM74DLJNyhW6a/", "7a/QZLET9IDreTiBfRn2PD", "b2S048IdVEGLb1C25UhBGg", "5cYFwxcgFMz6RewhsE/VdA", "04jFXZYsVLGLWHsn2mx0Ev", "fcKdBsvu9AA5wbo7fs7QJb", "aaNSkK9a5BOJVerstkidXR", "90K+Vgu/JB8rGLiq4sJeNZ", "19bB43meVBW4lgdvnzPguE", "964Nao70lIOpmOT6d1UioU", "12ewPIephIAZkWIlmwI2DG", "b09Qp+ySlE8LzcrlIXW0Yw", "78CaRVW05K3J8oXSQ/FzKV", "cf/TeBb35MiLpDHAMo/hHJ", "158lJbAihPMb3iOHrZvPo2", "85yrCTdKxH6p12tyyNXfMD", "8ajcZHBzNPFba+UvIytn8C", "3fe6N8qQNB4pmlbwDXc//C", "1eVY7cVPlB/4VTAR5bWPeP", "9emUBkXCtISb2LQlPBXJJs", "12a2fc589", "d5dvnHeCBNLpOS4elKdJV4", "482Hwz3jlFW5gAdMHolNC2", "e8qt0j51xGMZPl9GieHfJJ", "e70OYGm6tIg5WUJIFOQ+z7", "46+RTH75NFmKdAMpOjHXfr", "36h7MA9u1FWbxI5i/G6JnS", "f7+PSSiedPbYMZOtYIZY7N", "44wTC5jU9B+Y0keG1FMvE7", "acEySuPsZJpI1oFT//ysHQ", "29avtXLKRLJoM0A7Ay10FO", "b0IM3SauZIOZLoOWVpWPRi", "d5THtJ3D1GBZKNSU54OTA4", "c0xv3Z5zBPlb0dY6MxOv1v", "e2L5Nb0+tFEr1ea/I+rShi", "863qz8J3ZFW5HcwqEchIdF", "a7sTEPJNJFPbJmHbOIOwVR", "8cDjiDhfNMd6MB9kb7tbYX", "69lWdR0mdMGbqNF8FA0G7Q", "38pnMjv3JIQbDftOZBdoXB", "63L7V1nv5IFauMU2plnK5I", "17QrOWTlxJS5XN7GoSQC2K", "10CqFytU9KboMmYVoQISIW", "68HF1XuD1Gp6NToaRHwaVq", "f7kF1o2+JNXZ+xp4qyCRSV", "0fGaFgOHBL87HuxkfnYVOx", "01emlk1j9Fe4AS9HlGdCax", "85iTjwGgNB8JVJE/MSy7wH", "0eb+dmzW9K4Znq6yCojIZm", "87S+xooLVF5bm9VJrDLeUs", "5c/ZR82K5D6onIM2fZKvQl", "5e9LLalvpPgY6tM3Yyun1l", "18fh+aHzxIz6acJCw1NWwC", "53fqP5HcZNabkY4QvhWx+C", "69luZDU7JOqLci6M3AJBYQ"], ["node", "_spriteFrame", "_textureSetter", "root", "_parent", "_defaultClip", "data", "nodeBg", "prefabItem", "imgIcon1", "m_spine", "prefabStarAnimItem", "txtReward", "imgIcon", "spritePlus", "spriteTimes", "spriteDiv", "comNetError", "comLoading", "txtLoad", "loading", "imgProgressBar2", "nodeProgressIcon", "txtProgress2", "txtTips", "txtUID", "_N$barSprite", "scene", "effectPrefab", "prefabBuild", "abVersionJson"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_groupIndex", "_opacity", "_id", "_active", "_prefab", "_contentSize", "_parent", "_components", "_trs", "_children", "_color", "_anchorPoint", "_position", "_scale"], -2, 4, 5, 1, 9, 7, 2, 5, 5, 8, 8], "cc.Texture2D", ["cc.Sprite", ["_sizeMode", "_dstBlendFactor", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Node", ["_name", "_groupIndex", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_anchorPoint", "_children", "_color"], 0, 12, 4, 5, 1, 7, 5, 2, 5], ["cc.Widget", ["_alignFlags", "_left", "_bottom", "_originalWidth", "_originalHeight", "_right", "_top", "node"], -4, 1], ["cc.Label", ["_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_styleFlags", "_lineHeight", "_isSystemFontUsed", "_spacingX", "_materials", "node"], -5, 3, 1], ["cc.Node", ["_name", "_groupIndex", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint", "_position", "_scale"], 1, 1, 2, 4, 5, 7, 5, 5, 8, 8], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.AnimationClip", ["_name", "_duration", "speed", "wrapMode", "curveData"], -1, 11], ["cc.Prefab", ["_name", "optimizationPolicy"], 1], ["cc.AnimationClip", ["_name", "_duration", "curveData", "wrapMode"], -1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.Camera", ["_clearFlags", "_depth", "_cullingMask", "node"], 0, 1], ["cc.Node", ["_name", "_opacity", "_groupIndex", "_parent", "_children", "_components", "_prefab", "_color", "_contentSize"], 0, 1, 9, 9, 4, 5, 5], ["e73faifz+1A7KXnH5bqrH2c", ["node", "imgIcon1", "prefabStarAnimItem"], 3, 1, 1, 6], ["3b18fGgzSRLhKIQXMnTqGSZ", ["boneResDir", "node", "m_spine"], 2, 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_cacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -3, 1, 3], ["cc.Json<PERSON>set", ["_name", "json"], 1], ["867dfLajjtBkaU8o8Jc0Rgb", ["node", "imgIcon", "txtReward"], 3, 1, 1, 1], ["bae14CEqvlGS5gN0EiIUbft", ["layoutSpaceX", "node", "sprite", "spriteFrames"], 2, 1, 1, 3], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["d9fe2pnjDFDyIjDox9bbvzn", ["node", "nodeBg", "loading", "txtLoad", "comLoading", "comNetError", "prefabBuild"], 3, 1, 1, 1, 1, 1, 1, 6], ["a1721pQJL1BIZVRz/UCHk+q", ["node"], 3, 1], ["9b476PY+WdP27FsO0eOz9o9", ["node", "prefabItem"], 3, 1, 6], ["3a03a6F9PRGA6S+v+DSS5zG", ["node", "prefabItem"], 3, 1, 6], ["cc.<PERSON>", ["_fitWidth", "_fitHeight", "node", "_designResolution"], 1, 1, 5], ["ef6f6SX7FRAOrbvCxkiKHw2", ["node", "nodeBg", "txtUID", "txtTips", "persistNode", "txtProgress2", "nodeProgressIcon", "imgProgressBar2", "abVersionJson"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["cc.Scene", ["_name", "_active", "autoReleaseAssets", "_children", "_anchorPoint", "_trs"], 0, 2, 5, 7], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents"], 1, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["e26dfhMrQ9Cj4AWdwJmTRoy", ["interval", "maxQuantity", "useNodePool", "node", "effectPrefab"], 0, 1, 6], ["baf90GOqAhLOaIrPDeOTIUX", ["node", "loadingNode", "txtDesc"], 3, 1, 1, 1], ["d1521liRfhL+YT4pNH6L5zl", ["node"], 3, 1], ["85de6K7DBRA4buYsEG2oD/+", ["node"], 3, 1]], [[8, 0, 1, 2], [3, 4, 5, 6, 1], [8, 0, 1, 2, 2], [1, 0, 7, 8, 5, 6, 9, 2], [1, 0, 1, 7, 8, 5, 6, 9, 3], [3, 0, 4, 5, 6, 2], [1, 0, 2, 7, 8, 5, 6, 3], [17, 0, 1, 2, 1], [1, 0, 7, 8, 9, 2], [3, 2, 0, 4, 5, 6, 3], [1, 0, 7, 8, 5, 6, 2], [5, 0, 7, 2], [13, 2, 0, 1, 3, 4], [10, 0, 2], [1, 0, 2, 7, 8, 5, 11, 6, 9, 3], [1, 0, 1, 7, 10, 8, 5, 6, 9, 3], [8, 1, 2, 1], [3, 1, 0, 4, 5, 6, 3], [9, 0, 1, 4, 3], [29, 0, 1], [1, 0, 10, 8, 5, 6, 9, 2], [1, 0, 1, 7, 10, 5, 6, 9, 3], [1, 0, 7, 10, 5, 2], [1, 0, 2, 7, 8, 5, 6, 9, 3], [4, 0, 2, 1, 6, 9, 3, 4, 5, 4], [7, 0, 2, 3, 4, 5, 2], [8, 1, 1], [3, 0, 4, 5, 2], [11, 0, 1, 3, 2, 5], [9, 0, 4, 2], [5, 0, 3, 4, 7, 4], [10, 0, 1, 3], [1, 0, 10, 5, 6, 9, 2], [1, 0, 10, 8, 5, 12, 9, 2], [1, 0, 7, 10, 5, 6, 9, 2], [1, 0, 10, 5, 2], [1, 0, 7, 10, 5, 6, 2], [1, 0, 1, 3, 10, 8, 5, 6, 9, 4], [1, 0, 4, 1, 7, 10, 8, 5, 6, 4], [1, 0, 3, 10, 8, 6, 9, 3], [1, 0, 7, 10, 8, 5, 6, 9, 2], [1, 0, 1, 10, 8, 5, 6, 9, 3], [1, 0, 1, 7, 10, 8, 5, 6, 3], [1, 0, 1, 7, 10, 5, 6, 3], [1, 0, 1, 8, 5, 6, 3], [1, 0, 2, 1, 7, 5, 11, 6, 4], [1, 0, 1, 7, 8, 5, 11, 6, 13, 14, 3], [1, 0, 2, 1, 7, 8, 5, 11, 6, 4], [1, 0, 1, 7, 10, 5, 11, 6, 9, 3], [1, 0, 1, 8, 5, 6, 9, 3], [1, 0, 1, 7, 10, 5, 11, 6, 3], [1, 0, 1, 7, 8, 5, 6, 3], [1, 0, 7, 8, 5, 2], [1, 0, 1, 7, 8, 5, 6, 12, 13, 14, 9, 3], [1, 0, 1, 7, 8, 5, 11, 6, 12, 9, 3], [1, 0, 7, 10, 5, 9, 2], [4, 0, 6, 3, 4, 5, 8, 2], [4, 0, 9, 3, 4, 5, 7, 2], [4, 0, 1, 6, 3, 4, 5, 7, 3], [4, 0, 1, 6, 3, 4, 10, 5, 8, 7, 3], [4, 0, 1, 6, 3, 4, 5, 8, 7, 3], [7, 0, 2, 3, 4, 5, 6, 2], [7, 0, 1, 2, 3, 4, 7, 5, 8, 9, 10, 6, 3], [7, 0, 1, 2, 3, 4, 5, 6, 3], [14, 0, 1, 2, 3, 4, 5, 6, 7, 8, 4], [3, 4, 5, 1], [3, 1, 4, 5, 6, 2], [3, 3, 4, 5, 2], [3, 2, 0, 4, 5, 3], [15, 0, 1, 2, 1], [16, 0, 1, 2, 2], [18, 0, 1, 2, 3, 4, 5, 6, 7, 7], [11, 0, 1, 2, 4], [9, 0, 1, 2, 4, 4], [9, 0, 1, 3, 4, 4], [19, 0, 1, 3], [20, 0, 1, 2, 1], [12, 0, 1, 3, 4, 3], [12, 0, 1, 2, 3, 4, 4], [21, 0, 1, 2, 3, 2], [22, 0, 1, 3], [5, 0, 1, 5, 6, 2, 7, 6], [5, 0, 1, 7, 3], [5, 2, 7, 2], [23, 0, 1, 2, 3, 4, 5, 6, 1], [24, 0, 1], [25, 0, 1, 1], [26, 0, 1, 1], [27, 0, 1, 2, 3, 3], [28, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [30, 0, 1, 2, 3, 3], [31, 0, 1, 2, 3, 4, 5, 4], [32, 0, 1, 2, 3, 3], [33, 0, 1, 2, 3], [6, 3, 0, 4, 1, 2, 8, 6], [6, 0, 9, 8, 2], [6, 9, 8, 1], [6, 3, 0, 5, 6, 7, 4, 1, 2, 9, 8, 9], [6, 0, 5, 1, 2, 9, 8, 5], [34, 0, 1, 2, 2], [13, 0, 1, 3, 3], [35, 0, 1, 2, 3, 4, 4], [36, 0, 1, 2, 1], [37, 0, 1], [38, 0, 1]], [[[[13, "fly_item"], [32, "fly_item", [-2], [16, -1, 0], [5, 150, 150], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "img_icon", 1, [[27, 0, -3, [0]]], [2, "63W3aYb55ALJg1lFrPivGn", 1, 0], [5, 100, 100]]], 0, [0, 3, 1, 0, -1, 2, 0, 0, 2, 0, 6, 1, 3], [0], [-1], [0]], [[{"name": "0color", "rect": [0, 0, 40, 40], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [19]], [[{"name": "par_light", "rect": [3, 143, 96, 96], "offset": [0, 0], "originalSize": [96, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [8]], [[{"name": "8", "rect": [570, 2, 96, 123], "offset": [0, 0], "originalSize": [96, 123], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [3]], [[[31, "building_furniture", 2], [33, "building_furniture", [-6, -7], [[69, -3, -2, 8], [70, "prefab/spine", -5, -4]], [16, -1, 0], [0, 0.5, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [56, "img", 1, [[[7, -8, [1, 2, 3, 4, 5], 0], -9], 4, 1], [2, "40QOMfNsFMzr+JgAUGbyPa", 1, 0], [5, 107, 302], [0, 0.5, 0]], [65, 2, [6]], [34, "bone", 1, [-10], [2, "60y4xIHlhGC40rSEvFHuF1", 1, 0], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [61, "bone", 4, [-11], [2, "71+zUZ3jNP3LW+hznRvUSM", 1, 0], [5, 704, 728], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [71, "default", "idle", 1, 1, false, "idle", 5, [7]]], 0, [0, 3, 1, 0, 9, 3, 0, 0, 1, 0, 10, 6, 0, 0, 1, 0, -1, 2, 0, -2, 4, 0, 0, 2, 0, -2, 3, 0, -1, 5, 0, -1, 6, 0, 6, 1, 11], [0, 0, 0, 0, 0, 0, 0, 0, 0], [5, -1, -2, -3, -4, -5, -1, -1, 11], [10, 20, 21, 22, 10, 23, 0, 24, 25]], [[{"name": "word", "rect": [2, 2, 566, 122], "offset": [0, 0], "originalSize": [566, 122], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [3]], [[{"name": "3", "rect": [3, 321, 57, 82], "offset": [0, 0], "originalSize": [57, 82], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [["0,9729,9729,33071,33071,0,0,0", -1], [2], 0, [], [], []], [["0,9729,9729,33071,33071,0,0,0", -1], [2], 0, [], [], []], [[{"name": "7", "rect": [66, 232, 59, 80], "offset": [0, 0], "originalSize": [59, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[{"name": "quzizi", "rect": [553, 140, 106, 33], "offset": [0, 0], "originalSize": [106, 33], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [3]], [[{"name": "pro2", "rect": [51, 126, 500, 34], "offset": [0, 0], "originalSize": [500, 34], "capInsets": [14, 0, 16, 0]}], [0], 0, [0], [2], [3]], [[{"name": "1", "rect": [3, 3, 45, 81], "offset": [0, 0], "originalSize": [45, 81], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[[28, "anim_catenate", 1.4166666666666667, 2, {"paths": {"gray_3": {"props": {"active": [{"frame": 0, "value": true}]}}, "gray_2": {"props": {"active": [{"frame": 0, "value": true}]}}, "gray_1": {"props": {"active": [{"frame": 0, "value": true}]}}, "light_3": {"props": {"active": [{"frame": 0, "value": false}, {"frame": 1, "value": true}, {"frame": 1.4166666666666667, "value": true}]}}, "light_2": {"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.6666666666666666, "value": true}]}}, "light_1": {"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}]}}}}]], 0, 0, [], [], []], [[[72, "anim_build_in", 0.3333333333333333, {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": 255}]}, "paths": {"narrow": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "img1": {"props": {"opacity": [{"frame": 0, "value": 0}]}}}}]], 0, 0, [], [], []], [[{"name": "x", "rect": [3, 90, 58, 66], "offset": [0, 0], "originalSize": [58, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [["0,9729,9729,33071,33071,0,0,0", -1], [2], 0, [], [], []], [["0,9729,9729,33071,33071,0,0,0", -1], [2], 0, [], [], []], [[{"name": "par0", "rect": [143, 3, 52, 52], "offset": [0, 0], "originalSize": [52, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [8]], [[[28, "anim_rotate", 2.5, 2, {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 1.25, "value": 100}, {"frame": 2.5, "value": 255}], "angle": [{"frame": 0, "value": 0, "curve": "linear"}, {"frame": 0.6666666666666666, "value": -240}, {"frame": 1.25, "value": -360, "curve": "linear"}, {"frame": 2.5, "value": -720}]}}]], 0, 0, [], [], []], [[{"name": "light_2", "rect": [116, 494, 54, 26], "offset": [0, 0], "originalSize": [54, 26], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "par0", "rect": [0, 0, 52, 52], "offset": [0, 0], "originalSize": [52, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [26]], [[{"name": "gray_3", "rect": [173, 298, 81, 31], "offset": [0, 0], "originalSize": [81, 31], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "btn", "rect": [101, 623, 299, 95], "offset": [0, 0], "originalSize": [299, 95], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "add", "rect": [3, 162, 61, 64], "offset": [0, 0], "originalSize": [61, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[{"name": "gray_1", "rect": [101, 928, 20, 21], "offset": [0, 0], "originalSize": [20, 21], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "bg", "rect": [3, 494, 107, 108], "offset": [0, 0], "originalSize": [107, 108], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[[13, "click_screen"], [20, "click_screen", [-3, -4, -5], [[7, -2, [19], 18]], [16, -1, 0], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [22, "par", 1, [-6, -7, -8, -9, -10, -11, -12], [2, "09QL8d81FKwqXe4HG4DL1n", 1, 0]], [10, "0", 1, [[5, 0, -13, [0], 1]], [2, "21MHF8v6lKyLP1LV40emKq", 1, 0], [5, 40, 40]], [23, "ring1", 0, 1, [[5, 0, -14, [2], 3]], [2, "a4UQRieBROqrp5C5tgf6ux", 1, 0], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [23, "1", 0, 2, [[5, 0, -15, [4], 5]], [2, "8bD+lEMmBLN6gS1+RJ7y0Z", 1, 0], [5, 16, 16], [-26.537, 28.011, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "2", 0, 2, [[5, 0, -16, [6], 7]], [2, "acjQIl/HtHk7hpmvFQwnAG", 1, 0], [5, 16, 16]], [6, "3", 0, 2, [[5, 0, -17, [8], 9]], [2, "3ehf4pNWhFnodVepUtPcpJ", 1, 0], [5, 16, 16]], [6, "4", 0, 2, [[5, 0, -18, [10], 11]], [2, "27c75yzeBNJbpkgvO+F4M5", 1, 0], [5, 16, 16]], [6, "5", 0, 2, [[5, 0, -19, [12], 13]], [2, "fadcKjl0tLUJG0oQIFJCPc", 1, 0], [5, 16, 16]], [6, "6", 0, 2, [[5, 0, -20, [14], 15]], [2, "d50xjFd6dH864XIJsrj4KW", 1, 0], [5, 16, 16]], [6, "7", 0, 2, [[5, 0, -21, [16], 17]], [2, "80e6G2OuFHoaoE2KZgn9L8", 1, 0], [5, 16, 16]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 11, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 6, 1, 21], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 5, -1], [0, 27, 0, 28, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4, 11, 11]], [["7@34_0,9729,9729,33071,33071,0,0,0", -1], [2], 0, [], [], []], [[[73, "anim_build_shine", 1.3333333333333333, 2, [{}, "paths", 11, [{"shine": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": 255}, {"frame": 1.0833333333333333, "value": 255}, {"frame": 1.25, "value": 0}]}}, "cloud": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "hammer": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "broom": {"props": {"opacity": [{"frame": 0, "value": 0}]}}}, "shine/03", 11, [{}, "props", 11, [{"position": [{"frame": 0.23333333333333334, "value": [-150.487, 150.885, 0]}, {"frame": 1.3166666666666667, "value": [-150.11892307692307, 199.13353846153845, 0]}], "opacity": [{"frame": 0.48333333333333334, "value": 255}, {"frame": 0.65, "value": 0}, {"frame": 0.8166666666666667, "value": 255}, {"frame": 0.9833333333333333, "value": 0}, {"frame": 1.15, "value": 255}], "angle": [{"frame": 0.23333333333333334, "value": 0}, {"frame": 1.3166666666666667, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.23333333333333334}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.48333333333333334}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.65}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.8166666666666667}, "value", 8, [0, 0.5, 0.5]], [{"frame": 1.15}, "value", 8, [0, 0.2, 0.2]], [{"frame": 1.3166666666666667}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]], "shine/01", 11, [{}, "props", 11, [{"position": [{"frame": 0.16666666666666666, "value": [-71.7, 189.559, 0]}, {"frame": 1.25, "value": [-71.7, 216.4], "motionPath": []}], "opacity": [{"frame": 0.4166666666666667, "value": 255}, {"frame": 0.5833333333333334, "value": 0}, {"frame": 0.75, "value": 255}, {"frame": 0.9166666666666666, "value": 0}, {"frame": 1.0833333333333333, "value": 255}], "angle": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 1.25, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.16666666666666666}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.4166666666666667}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.5833333333333334}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.75}, "value", 8, [0, 0.5, 0.5]], [{"frame": 1.0833333333333333}, "value", 8, [0, 0.2, 0.2]], [{"frame": 1.25}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]], "shine/02", 11, [{}, "props", 11, [{"position": [{"frame": 0.13333333333333333, "value": [10.4, 165.8]}, {"frame": 1.2166666666666666, "value": [10.4, 211.8]}], "opacity": [{"frame": 0.38333333333333336, "value": 255}, {"frame": 0.55, "value": 0}, {"frame": 0.7166666666666667, "value": 255}, {"frame": 0.8833333333333333, "value": 0}, {"frame": 1.05, "value": 255}], "angle": [{"frame": 0.13333333333333333, "value": 0}, {"frame": 1.2166666666666666, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.13333333333333333}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.38333333333333336}, "value", 8, [0, 0.4, 0.4]], [{"frame": 0.55}, "value", 8, [0, 0.1, 0.1]], [{"frame": 0.7166666666666667}, "value", 8, [0, 0.3, 0.3]], [{"frame": 1.05}, "value", 8, [0, 0.1, 0.1]], [{"frame": 1.2166666666666666}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]], "shine/04", 11, [{}, "props", 11, [{"position": [{"frame": 0.16666666666666666, "value": [91.7, 43.9], "motionPath": [[93.3, 91.9, 93, 82.3, 93.3, 92.1], [93.5, 100.7, 93.3, 98.3, 93.5, 101.3], [93.4, 101.4, 93.4, 101.4, 93.4, 102.2]]}, {"frame": 1.25, "value": [93.6, 108.5], "motionPath": []}], "opacity": [{"frame": 0.4166666666666667, "value": 255}, {"frame": 0.5833333333333334, "value": 0}, {"frame": 0.75, "value": 255}, {"frame": 0.9166666666666666, "value": 0}, {"frame": 1.0833333333333333, "value": 255}], "angle": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 1.25, "value": -140}]}, "scale", 12, [[[{"frame": 0.16666666666666666}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.4166666666666667}, "value", 8, [0, 0.4, 0.4]], [{"frame": 0.5833333333333334}, "value", 8, [0, 0.1, 0.1]], [{"frame": 0.75}, "value", 8, [0, 0.3, 0.3]], [{"frame": 1.0833333333333333}, "value", 8, [0, 0.1, 0.1]], [{"frame": 1.25}, "value", 8, [0, 0.2, 0.2]]], 11, 11, 11, 11, 11, 11]]], "shine/05", 11, [{}, "props", 11, [{"position": [{"frame": 0.25, "value": [-101.167, 99.824, 0]}, {"frame": 1.3333333333333333, "value": [-100.932, 153.859, 0]}], "opacity": [{"frame": 0.5, "value": 255}, {"frame": 0.6666666666666666, "value": 0}, {"frame": 0.8333333333333334, "value": 255}, {"frame": 1, "value": 0}, {"frame": 1.1666666666666667, "value": 255}], "angle": [{"frame": 0.25, "value": 0}, {"frame": 1.3333333333333333, "value": -140}]}, "scale", 12, [[[{"frame": 0.25}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.5}, "value", 8, [0, 0.4, 0.4]], [{"frame": 0.6666666666666666}, "value", 8, [0, 0.1, 0.1]], [{"frame": 0.8333333333333334}, "value", 8, [0, 0.3, 0.3]], [{"frame": 1.1666666666666667}, "value", 8, [0, 0.1, 0.1]], [{"frame": 1.3333333333333333}, "value", 8, [0, 0.2, 0.2]]], 11, 11, 11, 11, 11, 11]]], "shine/06", 11, [{}, "props", 11, [{"position": [{"frame": 0.15, "value": [-50.171, -0.954, 0]}, {"frame": 1.2333333333333334, "value": [-49.936, 50.652, 0]}], "opacity": [{"frame": 0.4, "value": 255}, {"frame": 0.5666666666666667, "value": 0}, {"frame": 0.7333333333333333, "value": 255}, {"frame": 0.9, "value": 0}, {"frame": 1.0666666666666667, "value": 255}], "angle": [{"frame": 0.15, "value": 0}, {"frame": 1.2333333333333334, "value": -140}]}, "scale", 12, [[[{"frame": 0.15}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.4}, "value", 8, [0, 0.4, 0.4]], [{"frame": 0.5666666666666667}, "value", 8, [0, 0.1, 0.1]], [{"frame": 0.7333333333333333}, "value", 8, [0, 0.3, 0.3]], [{"frame": 1.0666666666666667}, "value", 8, [0, 0.1, 0.1]], [{"frame": 1.2333333333333334}, "value", 8, [0, 0.2, 0.2]]], 11, 11, 11, 11, 11, 11]]], "shine/07", 11, [{}, "props", 11, [{"position": [{"frame": 0.16666666666666666, "value": [-150.342, 23.33, 0]}, {"frame": 1.25, "value": [-150.107, 81.007, 0]}], "opacity": [{"frame": 0.4166666666666667, "value": 255}, {"frame": 0.5833333333333334, "value": 0}, {"frame": 0.75, "value": 255}, {"frame": 0.9166666666666666, "value": 0}, {"frame": 1.0833333333333333, "value": 255}], "angle": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 1.25, "value": -140}]}, "scale", 12, [[[{"frame": 0.16666666666666666}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.4166666666666667}, "value", 8, [0, 0.4, 0.4]], [{"frame": 0.5833333333333334}, "value", 8, [0, 0.1, 0.1]], [{"frame": 0.75}, "value", 8, [0, 0.3, 0.3]], [{"frame": 1.0833333333333333}, "value", 8, [0, 0.1, 0.1]], [{"frame": 1.25}, "value", 8, [0, 0.2, 0.2]]], 11, 11, 11, 11, 11, 11]]], "shine/08", 11, [{}, "props", 11, [{"position": [{"frame": 0.2, "value": [93.711, 139.286, 0]}, {"frame": 1.2833333333333334, "value": [94.553, 183.987, 0]}], "opacity": [{"frame": 0.45, "value": 255}, {"frame": 0.6166666666666667, "value": 0}, {"frame": 0.7833333333333333, "value": 255}, {"frame": 0.95, "value": 0}, {"frame": 1.1166666666666667, "value": 255}], "angle": [{"frame": 0.2, "value": 0}, {"frame": 1.2833333333333334, "value": -140}]}, "scale", 12, [[[{"frame": 0.2}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.45}, "value", 8, [0, 0.4, 0.4]], [{"frame": 0.6166666666666667}, "value", 8, [0, 0.1, 0.1]], [{"frame": 0.7833333333333333}, "value", 8, [0, 0.3, 0.3]], [{"frame": 1.1166666666666667}, "value", 8, [0, 0.1, 0.1]], [{"frame": 1.2833333333333334}, "value", 8, [0, 0.2, 0.2]]], 11, 11, 11, 11, 11, 11]]], "shine/09", 11, [{}, "props", 11, [{"position": [{"frame": 0.21666666666666667, "value": [125.003, 39.992, 0]}, {"frame": 1.3, "value": [124.937, 72.982, 0]}], "opacity": [{"frame": 0.4666666666666667, "value": 255}, {"frame": 0.6333333333333333, "value": 0}, {"frame": 0.8, "value": 255}, {"frame": 0.9666666666666667, "value": 0}, {"frame": 1.1333333333333333, "value": 255}], "angle": [{"frame": 0.21666666666666667, "value": 0}, {"frame": 1.3, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.21666666666666667}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.4666666666666667}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.6333333333333333}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.8}, "value", 8, [0, 0.5, 0.5]], [{"frame": 1.1333333333333333}, "value", 8, [0, 0.2, 0.2]], [{"frame": 1.3}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]], "shine/10", 11, [{}, "props", 11, [{"position": [{"frame": 0.2, "value": [5.054, 20.699, 0]}, {"frame": 1.2833333333333334, "value": [5.054, 59.053, 0]}], "opacity": [{"frame": 0.45, "value": 255}, {"frame": 0.6166666666666667, "value": 0}, {"frame": 0.7833333333333333, "value": 255}, {"frame": 0.95, "value": 0}, {"frame": 1.1166666666666667, "value": 255}], "angle": [{"frame": 0.2, "value": 0}, {"frame": 1.2833333333333334, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.2}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.45}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.6166666666666667}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.7833333333333333}, "value", 8, [0, 0.5, 0.5]], [{"frame": 1.1166666666666667}, "value", 8, [0, 0.2, 0.2]], [{"frame": 1.2833333333333334}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]], "shine/11", 11, [{}, "props", 11, [{"position": [{"frame": 0.16666666666666666, "value": [-198.022, 115.693, 0]}, {"frame": 1.25, "value": [-197.929, 141.484, 0]}], "opacity": [{"frame": 0.4166666666666667, "value": 255}, {"frame": 0.5833333333333334, "value": 0}, {"frame": 0.75, "value": 255}, {"frame": 0.9166666666666666, "value": 0}, {"frame": 1.0833333333333333, "value": 255}], "angle": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 1.25, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.16666666666666666}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.4166666666666667}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.5833333333333334}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.75}, "value", 8, [0, 0.5, 0.5]], [{"frame": 1.0833333333333333}, "value", 8, [0, 0.2, 0.2]], [{"frame": 1.25}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]], "shine/12", 11, [{}, "props", 11, [{"position": [{"frame": 0.1, "value": [-85.297, 31.834, 0]}, {"frame": 1.1833333333333333, "value": [-85.297, 65.02, 0]}], "opacity": [{"frame": 0.35, "value": 255}, {"frame": 0.5166666666666667, "value": 0}, {"frame": 0.6833333333333333, "value": 255}, {"frame": 0.85, "value": 0}, {"frame": 1.0166666666666666, "value": 255}], "angle": [{"frame": 0.1, "value": 0}, {"frame": 1.1833333333333333, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.1}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.35}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.5166666666666667}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.6833333333333333}, "value", 8, [0, 0.5, 0.5]], [{"frame": 1.0166666666666666}, "value", 8, [0, 0.2, 0.2]], [{"frame": 1.1833333333333333}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]], "shine/13", 11, [{}, "props", 11, [{"position": [{"frame": 0.13333333333333333, "value": [-14.593, 60.841, 0]}, {"frame": 1.2166666666666666, "value": [-14.621, 89.286, 0]}], "opacity": [{"frame": 0.38333333333333336, "value": 255}, {"frame": 0.55, "value": 0}, {"frame": 0.7166666666666667, "value": 255}, {"frame": 0.8833333333333333, "value": 0}, {"frame": 1.05, "value": 255}], "angle": [{"frame": 0.13333333333333333, "value": 0}, {"frame": 1.2166666666666666, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.13333333333333333}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.38333333333333336}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.55}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.7166666666666667}, "value", 8, [0, 0.5, 0.5]], [{"frame": 1.05}, "value", 8, [0, 0.2, 0.2]], [{"frame": 1.2166666666666666}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]], "shine/14", 11, [{}, "props", 11, [{"position": [{"frame": 0.16666666666666666, "value": [-129.199, -29.491, 0]}, {"frame": 1.25, "value": [-129.912, 6.923, 0]}], "opacity": [{"frame": 0.4166666666666667, "value": 255}, {"frame": 0.5833333333333334, "value": 0}, {"frame": 0.75, "value": 255}, {"frame": 0.9166666666666666, "value": 0}, {"frame": 1.0833333333333333, "value": 255}], "angle": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 1.25, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.16666666666666666}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.4166666666666667}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.5833333333333334}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.75}, "value", 8, [0, 0.5, 0.5]], [{"frame": 1.0833333333333333}, "value", 8, [0, 0.2, 0.2]], [{"frame": 1.25}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]], "shine/15", 11, [{}, "props", 11, [{"position": [{"frame": 0.2, "value": [-234.042, 82.798, 0]}, {"frame": 1.2833333333333334, "value": [-234.042, 106.888, 0]}], "opacity": [{"frame": 0.45, "value": 255}, {"frame": 0.6166666666666667, "value": 0}, {"frame": 0.7833333333333333, "value": 255}, {"frame": 0.95, "value": 0}, {"frame": 1.1166666666666667, "value": 255}], "angle": [{"frame": 0.2, "value": 0}, {"frame": 1.2833333333333334, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.2}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.45}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.6166666666666667}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.7833333333333333}, "value", 8, [0, 0.5, 0.5]], [{"frame": 1.1166666666666667}, "value", 8, [0, 0.2, 0.2]], [{"frame": 1.2833333333333334}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]], "shine/16", 11, [{}, "props", 11, [{"position": [{"frame": 0.16666666666666666, "value": [-71.7, 189.559, 0]}, {"frame": 1.25, "value": [-71.7, 216.4]}], "opacity": [{"frame": 0.4166666666666667, "value": 255}, {"frame": 0.5833333333333334, "value": 0}, {"frame": 0.75, "value": 255}, {"frame": 0.9166666666666666, "value": 0}, {"frame": 1.0833333333333333, "value": 255}], "angle": [{"frame": 0.16666666666666666, "value": 0}, {"frame": 1.25, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.16666666666666666}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.4166666666666667}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.5833333333333334}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.75}, "value", 8, [0, 0.5, 0.5]], [{"frame": 1.0833333333333333}, "value", 8, [0, 0.2, 0.2]], [{"frame": 1.25}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]], "shine/17", 11, [{}, "props", 11, [{"position": [{"frame": 0.21666666666666667, "value": [58.831, -17.115, 0]}, {"frame": 1.3, "value": [59.122, 26.042, 0]}], "opacity": [{"frame": 0.4666666666666667, "value": 255}, {"frame": 0.6333333333333333, "value": 0}, {"frame": 0.8, "value": 255}, {"frame": 0.9666666666666667, "value": 0}, {"frame": 1.1333333333333333, "value": 255}], "angle": [{"frame": 0.21666666666666667, "value": 0}, {"frame": 1.3, "value": -140.46153846153845}]}, "scale", 12, [[[{"frame": 0.21666666666666667}, "value", 8, [0, 0.2, 0.2]], [{"frame": 0.4666666666666667}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.6333333333333333}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.8}, "value", 8, [0, 0.5, 0.5]], [{"frame": 1.1333333333333333}, "value", 8, [0, 0.2, 0.2]], [{"frame": 1.3}, "value", 8, [0, 0.2, 0.3]]], 11, 11, 11, 11, 11, 11]]]]]]], 0, 0, [], [], []], [["7@34_0,9729,9729,33071,33071,0,0,0", -1], [2], 0, [], [], []], [[{"name": "beijing", "rect": [0, 0, 750, 1624], "offset": [0, 0], "originalSize": [750, 1624], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [29]], [[[18, "anim_popup_in1", 0.35, [{}, "paths", 11, [{"blackbg": {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": 180}]}}}, "content", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.15, "value": 255}]}, "scale", 12, [[[{"frame": 0, "curve": "quadOut"}, "value", 8, [0, 0.3, 0.3]], [{"frame": 0.18333333333333332}, "value", 8, [0, 1.06, 1.06]], [{"frame": 0.35}, "value", 8, [0, 1, 1]]], 11, 11, 11]]]]]]], 0, 0, [], [], []], [[[75, "version", {"newyear_main": {"ver": "v161", "md5": "9402dfa01064efdeab548dcc182948c5"}, "newyear_reward": {"ver": "v161", "md5": "11b66ea2c701aaf64e31a46588b5eb2c"}, "penguin_confirm": {"ver": "v161", "md5": "1c0a193da2eed59123c1b45f8be57819"}, "penguin_info": {"ver": "v161", "md5": "4a20b4687b7f7095a70343a127f8a7e0"}, "penguin_join": {"ver": "v161", "md5": "fbb5f4f526045286c9f5c16fee46f2ae"}, "penguin_main": {"ver": "v161", "md5": "5e3e39aa0e76056e3e106d374793248d"}, "penguin_revive": {"ver": "v161", "md5": "af46f1e4132c6f88b18b6aceb1b7547d"}, "penguin_reward": {"ver": "v161", "md5": "95b751aeeb91ca9ba7fdfdc67e011b6f"}, "penguin_start": {"ver": "v161", "md5": "7e89bd21b0cfef225ab21f2e59f03db5"}, "snowman_main": {"ver": "v161", "md5": "da54d4a65c9ec0a47e1c66c730ba07ca"}, "snowman_reward": {"ver": "v161", "md5": "616488ff0418d66629ae7ec4f4f46e83"}, "volcano_again": {"ver": "v161", "md5": "17e7a00bf7664f7efc4df8d29b9c3459"}, "volcano_confirm": {"ver": "v161", "md5": "557f44316e6793df4a83fd740ace82c4"}, "volcano_info": {"ver": "v161", "md5": "6d154fc2d9403eea73bce43b0540fffb"}, "volcano_join": {"ver": "v161", "md5": "14e417ad58eee4f97062d0c126fe0f5b"}, "volcano_main": {"ver": "v161", "md5": "0d531ab1982f0a7b9a95461cbea1f590"}, "volcano_revive": {"ver": "v161", "md5": "1e30971fe824394cb2090a5aca838e7e"}, "volcano_reward": {"ver": "v161", "md5": "2fdf67b3fdee2c5eb801b1be8438c791"}, "volcano_start": {"ver": "v161", "md5": "433823e43a591967ed3911f2201718d2"}, "cardgacha_gacha": {"ver": "v161", "md5": "8a338693881a353b299d3738bf95d4b5"}, "cardgacha_gachareward": {"ver": "v161", "md5": "07f566e816e69ec619bcd328473baac9"}, "cardgacha_renew": {"ver": "v161", "md5": "612f06ca05c93e08c9820817aa4a43b2"}, "cards_bigcard": {"ver": "v161", "md5": "a655ad4e58ec9b81873554e383f05fb6"}, "cards_cardoverreward": {"ver": "v161", "md5": "50deb839326d59e466664c7d997600b0"}, "cards_cardsover": {"ver": "v161", "md5": "38e08dd14b326b38eee4c03f758db095"}, "cards_cardsover_dragon": {"ver": "v161", "md5": "1567202f38c3a76683f660ba69f23d20"}, "cards_choose": {"ver": "v161", "md5": "0e94a78713ec0a32bee6f2bc22b91994"}, "cards_collect": {"ver": "v161", "md5": "87be9a554824b350a8e809ead0aaea66"}, "cards_complete": {"ver": "v161", "md5": "4318bee8dc70843ee8c03cc916cf1c06"}, "cards_dragoncard": {"ver": "v161", "md5": "11597121817ff223b010997c8390d43e"}, "cards_exchange": {"ver": "v161", "md5": "3a49d9a131b0914030a1eba0c38272fe"}, "cards_giveyes": {"ver": "v161", "md5": "387427aeaa5b843fe6ad4e9c082238b0"}, "cards_info": {"ver": "v161", "md5": "d4b92368f791323ecbf95e09bbdb7ccd"}, "cards_main": {"ver": "v161", "md5": "c3dcc9d39bf887ff6e033fc83b1530e0"}, "cards_main_dargon": {"ver": "v161", "md5": "a606abec09720d02ac9dfa6b46499e05"}, "cards_newcard": {"ver": "v161", "md5": "3cbf127767a77cef7e150f04e1e97156"}, "cards_openbox": {"ver": "v161", "md5": "********************************"}, "cards_preview": {"ver": "v161", "md5": "77ae9e1a01d0f03bfbd4637cbbdbf232"}, "cards_preview_dragon": {"ver": "v161", "md5": "129e55cb7081b638fef8407733039cd2"}, "cards_save": {"ver": "v161", "md5": "015f99aff07a700553bf5d2e5d6beb66"}, "cards_tipinfo": {"ver": "v161", "md5": "e266b848448044c4c31164c196477080"}, "cards_wish": {"ver": "v161", "md5": "b6f82478d3d68b737c98f5a723f53332"}, "friend_info": {"ver": "v161", "md5": "13b605b230a81bb2898d451d700c2e0e"}, "friend_invite": {"ver": "v161", "md5": "ea385247b23c113c8aa17e6d505cbbec"}, "friend_main": {"ver": "v161", "md5": "d27945e84e826628159f5fd1ef866381"}, "friend_note": {"ver": "v161", "md5": "be61c517fa705c459dca462f94a9f136"}, "friend_ok": {"ver": "v161", "md5": "84afc387eb56d865d86a435ed7455421"}, "gift_center": {"ver": "v161", "md5": "26df6e407726d16acf11dea22f0dab57"}, "gm_opt": {"ver": "v161", "md5": "97e3bcef3cb48bda2abca61569421615"}, "cake_info": {"ver": "v161", "md5": "d4758c4c2fb943d22edd937dbe35c616"}, "cake_invite": {"ver": "v161", "md5": "2401d7d89086f7cccafdde8fcfc0d28a"}, "cake_lottery": {"ver": "v161", "md5": "26e82dc3fdd30a2a65182b47741df548"}, "cake_main": {"ver": "v161", "md5": "c84949d79a459b83040e8fc5bd5e98b6"}, "cake_start": {"ver": "v161", "md5": "df8826a3d940aaacfc77f3ad6366d82e"}, "passport_dragonyear_main": {"ver": "v161", "md5": "69df90901f6730cf15360df21dbeb75b"}, "passport_dragonyear_pay": {"ver": "v161", "md5": "3fefcbdd2733702bc36abc6f0454bb77"}, "passport_dragonyear_pay_info": {"ver": "v161", "md5": "c6b184d2df672ac96ed004e090372c21"}, "passport_dragonyear_taskover": {"ver": "v161", "md5": "b950355980a13b8018ae058f9d9b642b"}, "passport_feast_main": {"ver": "v161", "md5": "7db71388c1bf9db2072895c81ec7edbc"}, "passport_feast_pay": {"ver": "v161", "md5": "e81814ea2c9f56a75acc63d9123c151f"}, "passport_feast_pay_info": {"ver": "v161", "md5": "5fc7837e58f8271d4f5e57788c548142"}, "passport_feast_preview": {"ver": "v161", "md5": "2ad5dd94527665f956ec43d60a5d8ca7"}, "passport_feast_taskover": {"ver": "v161", "md5": "937d80ef70780f2b66df8795c9336b2e"}, "passport_purewhite_main": {"ver": "v161", "md5": "a0a884d70b75e16177cb7006764fbd52"}, "passport_purewhite_pay": {"ver": "v161", "md5": "1f3a0eafadd59e010fc7f3daf96b4d1d"}, "passport_purewhite_pay_info": {"ver": "v161", "md5": "3698f3cc172057b384cdb3064f2a80b4"}, "passport_purewhite_taskover": {"ver": "v161", "md5": "8d2e090a5c01c18b1096e1c397e59cc0"}, "paygift_actcake": {"ver": "v161", "md5": "6dcd7b6c820f6b1152bf55073433ed08"}, "paygift_diamondminer": {"ver": "v161", "md5": "86a050949dc54ad32136ff28ea36e60c"}, "paygift_enjoysilk": {"ver": "v161", "md5": "22b91bbe49d36475811b1264833eccc4"}, "paygift_lanternhigh": {"ver": "v161", "md5": "5ba0b18e10ac0ba9fd34c1c0dea6f663"}, "paygift_skykoi": {"ver": "v161", "md5": "bbaca98f96039b30fcf17f862815f6ae"}, "paygift_snowman": {"ver": "v161", "md5": "2d5928ed73bb2190a0ce05a6997d4e8f"}, "paygift_springletter": {"ver": "v161", "md5": "de17d7b1203a0298b33d6ae522c5d365"}, "paygift_surenew1": {"ver": "v161", "md5": "c5536183bc408711c00cbcb40b998cd5"}, "paygift_wintermoon": {"ver": "v161", "md5": "60906e221019146efda471290c20c4d9"}}]], 0, 0, [], [], []], [[{"name": "mask", "rect": [614, 262, 50, 50], "offset": [0, 0], "originalSize": [50, 50], "capInsets": [2, 3, 5, 3]}], [0], 0, [0], [2], [3]], [[{"name": "pro_bg2", "rect": [2, 127, 276, 47], "offset": [0, 0], "originalSize": [276, 47], "rotated": 1, "capInsets": [32, 0, 38, 0]}], [0], 0, [0], [2], [3]], [[{"name": "txt", "rect": [3, 623, 396, 92], "offset": [0, 0], "originalSize": [396, 92], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [["7@34_0,9729,9729,33071,33071,0,0,0", -1], [2], 0, [], [], []], [[[18, "anim_build_out", 0.08333333333333333, [{"paths": {"narrow": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "img1": {"props": {"opacity": [{"frame": 0, "value": 0}]}}}}, "props", 11, [{"opacity": [{"frame": 0, "value": 255}, {"frame": 0.08333333333333333, "value": 0}], "position": [{"frame": 0, "value": [0, 0, 0]}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 1, 1]]], 11]]]]], 0, 0, [], [], []], [[[29, "anim_build_none", [{"paths": {"narrow": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "img1": {"props": {"opacity": [{"frame": 0, "value": 0}]}}}}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}], "position": [{"frame": 0, "value": [0, 0, 0]}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 1, 1]]], 11]]]]], 0, 0, [], [], []], [[{"name": "5", "rect": [66, 318, 57, 83], "offset": [0, 0], "originalSize": [57, 83], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[{"name": "0", "rect": [3, 497, 58, 82], "offset": [0, 0], "originalSize": [58, 82], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[[13, "furniture_float"], [20, "furniture_float", [-6], [[7, -2, [23], 22], [76, -5, -4, -3]], [26, -1], [5, 150, 150], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [35, "effect", [-7, -8, -9, -10, -11], [0, "78tikFEdlJ96H7lsf0lAjO", 1]], [57, "num", [-14], [[[77, 1, 1, -12, [5, 56, 60]], -13], 4, 1], [0, "eftlg/KoFEkKSMUaC/SzOT", 1], [5, 56, 60], [0, -40, 0, 0, 0, 0, 1, 0.5, 0.5, 0.7]], [22, "anim", 1, [2, -15], [0, "6azy+2aa1D2blVNvG3riBH", 1]], [36, "coin_tip", 4, [-16, 3], [0, "01CipNQC9GM6sSS+1n2sh1", 1], [5, 81.4, 100]], [6, "light00", 0, 2, [[66, 1, -17, [0], 1]], [0, "a0XbJ/Jh5AYIS99xWEZLPk", 1], [5, 108, 108]], [14, "1", 0, 2, [[17, 1, 0, -18, [2], 3]], [0, "f8QO79pl9Gr5Kr0qF8mkIF", 1], [4, 4290902015], [5, 30, 30], [-22.493, 11.813, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "2", 0, 2, [[17, 1, 0, -19, [4], 5]], [0, "0fRVsCaSVMHYAa5/xNoQ13", 1], [4, 4290902015], [5, 30, 30], [4.054, 11.425, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "3", 0, 2, [[17, 1, 0, -20, [6], 7]], [0, "049YUNinBG4YG0exNQOSG4", 1], [4, 4290902015], [5, 30, 30], [12.899, 19.165, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "4", 0, 2, [[17, 1, 0, -21, [8], 9]], [0, "82rWbk/MdFpKIAK8N+pKbY", 1], [4, 4290902015], [5, 30, 30], [8.108, -13.637, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "coin_icon1", 5, [-22], [0, "62cmyGb/5Ff5/XgEyYzVn8", 1], [5, 100, 100]], [27, 0, 11, [10]], [25, "sprite", 3, [-23], [0, "99CNaSbBhDp4yJxXiYlw81", 1], [5, 56, 80]], [67, false, 13, [11]], [79, -4, 3, 14, [12, 13, 14, 15, 16, 17, 18, 19, 20, 21]]], 0, [0, 3, 1, 0, 0, 1, 0, 12, 15, 0, 13, 12, 0, 0, 1, 0, -1, 4, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 9, 0, -5, 10, 0, 0, 3, 0, -2, 15, 0, -1, 13, 0, -2, 5, 0, -1, 11, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, -1, 12, 0, -1, 14, 0, 6, 1, 2, 4, 4, 3, 4, 5, 23], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 15, 15, 15], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, 5, -1, 1, 14, 15, 16], [0, 30, 0, 9, 0, 9, 0, 9, 0, 9, 0, 0, 12, 31, 32, 33, 34, 35, 36, 37, 38, 39, 13, 13, 12, 40, 41, 42]], [[{"name": "8", "rect": [67, 407, 58, 82], "offset": [0, 0], "originalSize": [58, 82], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[{"name": "title", "rect": [3, 413, 156, 41], "offset": [0, 0], "originalSize": [156, 41], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "gamelogo", "rect": [51, 162, 469, 254], "offset": [0, 0], "originalSize": [469, 254], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [3]], [["0,9729,9729,33071,33071,0,0,0", -1], [2], 0, [], [], []], [[{"name": "9", "rect": [67, 495, 58, 82], "offset": [0, 0], "originalSize": [58, 82], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[{"name": "light0", "rect": [3, 3, 134, 134], "offset": [0, 0], "originalSize": [134, 134], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [8]], [[{"name": "img_bg", "rect": [3, 298, 164, 109], "offset": [0, 0], "originalSize": [164, 109], "capInsets": [40, 40, 40, 40]}], [0], 0, [0], [2], [1]], [[{"name": "light00", "rect": [3, 3, 108, 108], "offset": [0, 0], "originalSize": [108, 108], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [43]], [[{"name": "2", "rect": [3, 409, 57, 82], "offset": [0, 0], "originalSize": [57, 82], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[{"name": "light3", "rect": [105, 143, 96, 96], "offset": [0, 0], "originalSize": [96, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [8]], [[{"name": "light3", "rect": [0, 0, 96, 96], "offset": [0, 0], "originalSize": [96, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [44]], [[{"name": "div", "rect": [3, 232, 48, 83], "offset": [0, 0], "originalSize": [48, 83], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[[80, "LoadScene", null], [37, "PersistNode", 6, "09drqoEGZPFqdbr3UwMwVY", [-12, -13, -14, -15, -16, -17], [[30, 45, 750, 1624, -2], [84, -8, -7, -6, -5, -4, -3, 57], [85, -9], [86, -10, 58], [87, -11, 59]], [26, -1], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "popwin_net_error", false, 6, 1, [-22, -23], [[[30, 45, 750, 1334, -19], [7, -20, [42], 41], -21], 4, 4, 1], [0, "6aajUHADxF+5tDzzZoQ9QX", -18], [5, 750, 1334]], [38, "touch_net_error", false, 6, 1, [-26, -27], [[81, 45, -125, -125, -333, -333, -25]], [0, "25xex1mr5IaZbzM9gXTsZ0", -24], [5, 1000, 2000]], [39, "<PERSON><PERSON>", "c4yrwaYLNCzrciPLUPaeel", [-37, -38, -39, -40, -41, -42], [[88, true, false, -28, [5, 750, 1334]], [89, -35, -34, -33, -32, 1, -31, -30, -29, 66], [11, 45, -36]], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "catenate", 3, [-44, -45, -46, -47, -48, -49, -50], [[7, -43, [23], 22]], [0, "f8HvaTQxxGLIsDR5xcKrWa", 3], [5, 200, 200], [0, 363.968, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "Bottom", 6, [-52, -53, -54, -55, -56, -57], [[11, 4, -51]], [0, "edlKX4TdFOIqAE64towy4i", 1], [5, 750, 200], [0, -567, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "touch_loading", false, 6, 1, [-61, -62, -63], [[[11, 45, -58], [19, -59], -60], 4, 4, 1], [0, "4fWsCK90tDvqIjbAK4aaCs", 1], [5, 750, 1334]], [42, "loading", 6, 1, [-65, 6, -66], [[11, 45, -64]], [0, "29fhEtAwpILa9WbsmLKwiR", 1], [5, 750, 1334]], [43, "content", 6, 2, [-67, -68, -69, -70], [0, "9dC7dqnp1FuJUVzvuKUxlQ", 2], [5, 750, 1334]], [15, "title", 6, 9, [-72, -73, -74], [[78, 1, 1, 25, -71, [5, 497, 60]]], [0, "81zZRVlwtGrJE/KMxrsfoa", 2], [5, 497, 60], [1.577, 200.546, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "beijing", 6, [[5, 0, -75, [64], 65]], [0, "0aC+r8NwtGX5qEp3ba1W76", 1], [5, 750, 1624]], [64, "BlackBg", 140, 6, 7, [[45, "click", 190, 6, -78, [0, "24wxhKy7tBZacSDTQxzkqJ", 1], [4, 4278190080], [5, 750, 1800]]], [[19, -76], [9, 1, 0, -77, [1], 2]], [0, "28ushRcnBBX6Pi9dWfErH+", 1], [4, 4278190080], [5, 750, 1800]], [46, "loading", 6, 7, [[5, 0, -79, [3], 4], [7, -80, [6], 5]], [0, "bcs+1+4JROSZMBmoo2Uvr3", 1], [4, 4290963194], [5, 90, 90], [1, 0, 0, 0], [1, 1, 1, 1]], [47, "BlackBg", 120, 6, 2, [[9, 1, 0, -82, [25], 26], [19, -83]], [0, "31eM7LbmBHx7oHO13I/Qmt", -81], [4, 4278190080], [5, 2500, 3600]], [48, "mask", 6, 6, [-84, -85, -86], [0, "eajNxdSkFN1Z9hq9+nX0Du", 1], [4, 4278190080], [5, 2000, 230], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "progressBar", 6, 6, [-90], [[9, 1, 0, -87, [50], 51], [90, 521, 0, -89, -88]], [0, "7cH0AHuEFAF5oV3MjClr1H", 1], [5, 510, 46], [0, 130.63, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "top", 6, 8, [-92, -93], [[11, 1, -91]], [0, "63pe80DZhNo44QRrPtTF0N", 1], [5, 750, 200], [0, 567, 0, 0, 0, 0, 1, 1, 1, 1]], [91, "New Node", false, true, [4, 1], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "bg", 6, 9, [-95], [[19, -94]], [0, "60arcCt0NOWYjWUrCVnAP2", 2], [5, 750, 2000], [0, 36.484, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "icon", 6, 9, [-96, -97], [0, "c10w7HAv1ESLSChSl5nRWq", 2], [5, 550, 310], [0, 40.056, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "btn_go", 6, [[1, -98, [39], 40], [92, 1.1, 3, -99, [[93, "85de6K7DBRA4buYsEG2oD/+", "onRetry", 2]]]], [0, "5cJtf9JOxBNKsFwKO6jZM0", 2], [5, 299, 95], [0, 28.194, 0, 0, 0, 0, 1, 1, 1, 0]], [94, "快想办法让杰茜别再受冻……", 24, 1, 1, 1, [56]], [58, "tip", 6, 6, [[22, [99, 3, -100, [4, 2516582400]]], 1, 4], [0, "d2kgo3P3dIjIZQivv8XrSk", 1], [5, 318, 56.4], [0, 177.519, 0, 0, 0, 0, 1, 1, 1, 1]], [59, "progressInner", 6, 16, [[[82, 8, 4.5, -101], -102], 4, 1], [0, "b9Wxnpx6RGwLyG3I6qwphT", 1], [4, 4292784383], [5, 500, 34], [0, 0, 0.5], [-250.5, 2.369, 0, 0, 0, 0, 1, 1, 1, 1]], [68, 3, 0, 24, [49]], [4, "candy", 6, 6, [[1, -103, [52], 53]], [0, "a2ANeDexJFkoNuDwFBuXMt", 1], [5, 63, 53], [-255, 130, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "uid", 6, 6, [[-104, [83, 309.84, -105]], 1, 4], [0, "076qFROqNA87moLPgKRS89", 1], [5, 0, 40.32], [0, 1, 0.5], [356, 59.411, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Main Camera", 4, [[100, 7, -1, -106]], [0, 0, 579.3709951317895, 0, 0, 0, 1, 1, 1, 1]], [8, "BgCamera", 4, [[12, 16, 2, 10, -107]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [8, "UICamera", 4, [[12, 2, 2, 40, -108]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [8, "TopCamera", 4, [[12, 256, 0, 70, -109]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [8, "CurtainCamera", 4, [[12, 64, 0, 60, -110]], [0, 0, 336.11257819690115, 0, 0, 0, 1, 1, 1, 1]], [8, "BigMapCamera", 4, [[12, 512, 0, 15, -111]], [0, 0, 930.1112836644871, 0, 0, 0, 1, 1, 1, 1]], [50, "content_2", 6, 8, [11], [0, "c23M+8MA9PN5NfbzWwQhnQ", 1], [4, 4290766324], [5, 1500, 1660]], [51, "node_touch", 6, 1, [[101, 0.03, 10, true, -112, 0]], [0, "70Oe9de8lKEpBLmZa1RAF8", 1], [5, 750, 2000]], [62, "desc", 6, 7, [-113], [0, "e9D07tynBO3K+ZBr1yOX6i", 1], [4, 4291028730], [5, 0, 50.4], [0, 0.5, 1], [1, 0, 0, 0], [1, 1, 1, 1], [0, -80, 0, 0, 0, 0, 1, 1, 1, 1]], [95, 26, 36, [7]], [102, 7, 13, 37], [52, "robotRank", 1, [[103, -114]], [0, "eeGMae8UtIjrTLbB0xS778", 1]], [10, "bg", 5, [[1, -115, [8], 9]], [0, "34lmoa3AlOCpCpFUK3vf7q", 3], [5, 107, 108]], [3, "gray_3", 5, [[1, -116, [10], 11]], [0, "05fl+4qvBK34ATrXDL2DGj", 3], [5, 81, 31], [0, 19.021, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "gray_2", 5, [[1, -117, [12], 13]], [0, "96TzsfL5hFIZ1w+ARV+YPZ", 3], [5, 54, 26]], [3, "gray_1", 5, [[1, -118, [14], 15]], [0, "b0/gpXhZtO3KDar3rg0YJf", 3], [5, 20, 21], [0, -18.115, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "light_3", 5, [[1, -119, [16], 17]], [0, "75V9QEW6VLU5TszAq51eBn", 3], [5, 81, 31], [0, 19.021, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "light_2", 5, [[1, -120, [18], 19]], [0, "f3CN3QhjlGmp3FDENG4Jqr", 3], [5, 54, 26]], [3, "light_1", 5, [[1, -121, [20], 21]], [0, "93au7ppP5HP7cfuUbFNKbj", 3], [5, 20, 21], [0, -18.115, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "desc", 6, 3, [[96, -122, [24]]], [0, "219fMlCR5FYJ4s+O9599dJ", 3], [5, 0, 50.4], [0, 0.5, 1], [1, 0, 0, 0], [1, 1, 1, 1], [0, 157.869, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "bg", 6, 19, [[9, 1, 0, -123, [27], 28]], [0, "87VG6/N9pLKZfF4sn1S6k0", 2], [5, 665, 480], [1.858, -34.288, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_left", 6, 10, [[1, -124, [29], 30]], [0, "a2DcGOhApOMLhE0Jy9qpc7", 2], [5, 145, 28], [-176, -1.116, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "tittle", 6, 10, [[1, -125, [31], 32]], [0, "23rmogLNVFU4m45oKgOdAl", 2], [5, 156, 41], [-0.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_right", 6, 10, [[1, -126, [33], 34]], [0, "d7m3iH0ihAbKJW3eHi60H5", 2], [5, 146, 28], [175.5, -1.116, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "img_bg", 6, 20, [[9, 1, 0, -127, [35], 36]], [0, "635MRt8WNC0JSdEa9L7MlP", 2], [5, 538, 200], [0, -0.473, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "txt_1", 6, 20, [[1, -128, [37], 38]], [0, "5edayAArZGNYTs8l9yOTrf", 2], [5, 396, 92], [0, -2.828, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "bottom", 6, 9, [21], [0, "9eSv8yjPVHb68WQhcmhwmK", 2], [5, 600, 150], [0.591, -159.38, 0, 0, 0, 0, 1, 1, 1, 1]], [104, 2], [54, "bg", 6, 15, [[9, 1, 0, -129, [43], 44]], [0, "09IJOsGj9MBKhEpAmSJPkX", 1], [4, 4278190080], [5, 2000, 500], [0, 0.5, 1], [0, 119.768, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "quzizi", 6, 15, [[1, -130, [45], 46]], [0, "6bp8iWk2JPrI6O43Uwig8f", 1], [5, 106, 33], [0, 87.957, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "word", 6, 15, [[1, -131, [47], 48]], [0, "9aAN2X8qJJ66GtVeCIq93E", 1], [5, 566, 122], [0, 2.693, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "per", 6, 6, [-132], [0, "1fMR3mAWtIgrfjLQT8U66U", 1], [5, 48.04, 36], [0, 131.997, 0, 0, 0, 0, 1, 1, 1, 1]], [63, "txtProgress", 6, 59, [-133], [0, "bfmJ6ujixJcqhJ4om/k0Nu", 1], [5, 66.48, 63], [0, 2, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [97, "0%", 46, 50, false, -1, 1, 1, 1, 60, [54]], [98, 20, 32, 1, 1, 27, [55]], [4, "8", 6, 17, [[1, -134, [60], 61]], [0, "549pmYwjRC7odAHKEuhfqC", 1], [5, 96, 123], [-308, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "gamelogo", 6, 17, [[1, -135, [62], 63]], [0, "10d/5E2glBLqag3nNqiosD", 1], [5, 469, 254], [0, -170, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 17, 55, 0, 18, 38, 0, 19, 22, 0, 20, 8, 0, 7, 11, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 8, 0, -2, 35, 0, -3, 7, 0, -4, 39, 0, -5, 3, 0, -6, 2, 0, 3, 2, 0, 0, 2, 0, 0, 2, 0, -3, 55, 0, -1, 14, 0, -2, 9, 0, 3, 3, 0, 0, 3, 0, -1, 5, 0, -2, 47, 0, 0, 4, 0, 21, 25, 0, 22, 26, 0, 23, 61, 0, 24, 22, 0, 25, 62, 0, 7, 11, 0, 0, 4, 0, 0, 4, 0, -1, 28, 0, -2, 29, 0, -3, 30, 0, -4, 31, 0, -5, 32, 0, -6, 33, 0, 0, 5, 0, -1, 40, 0, -2, 41, 0, -3, 42, 0, -4, 43, 0, -5, 44, 0, -6, 45, 0, -7, 46, 0, 0, 6, 0, -1, 15, 0, -2, 23, 0, -3, 16, 0, -4, 26, 0, -5, 59, 0, -6, 27, 0, 0, 7, 0, 0, 7, 0, -3, 38, 0, -1, 12, 0, -2, 13, 0, -3, 36, 0, 0, 8, 0, -1, 34, 0, -3, 17, 0, -1, 19, 0, -2, 10, 0, -3, 20, 0, -4, 54, 0, 0, 10, 0, -1, 49, 0, -2, 50, 0, -3, 51, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 4, 12, 0, 0, 13, 0, 0, 13, 0, 3, 14, 0, 0, 14, 0, 0, 14, 0, -1, 56, 0, -2, 57, 0, -3, 58, 0, 0, 16, 0, 26, 25, 0, 0, 16, 0, -1, 24, 0, 0, 17, 0, -1, 63, 0, -2, 64, 0, 0, 19, 0, -1, 48, 0, -1, 52, 0, -2, 53, 0, 0, 21, 0, 0, 21, 0, 0, 23, 0, 0, 24, 0, -2, 25, 0, 0, 26, 0, -1, 62, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 35, 0, -1, 37, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, -1, 60, 0, -1, 61, 0, 0, 63, 0, 0, 64, 0, 27, 18, 1, 4, 18, 4, 4, 18, 6, 4, 8, 11, 4, 34, 21, 4, 54, 22, 0, 23, 135], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25], [28, -1, 1, -1, 1, 5, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 5, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 5, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, 29, 8, 8, -1, 1, -1, 1, -1, 1, 30, 1], [45, 0, 46, 0, 47, 14, 14, 0, 0, 48, 0, 49, 0, 50, 0, 51, 0, 52, 0, 53, 0, 54, 15, 15, 0, 0, 55, 0, 56, 0, 57, 0, 58, 0, 59, 0, 60, 0, 61, 0, 62, 16, 16, 0, 63, 0, 64, 0, 65, 0, 0, 66, 0, 67, 0, 0, 0, 68, 69, 70, 0, 71, 0, 72, 0, 73, 74, 75]], [[{"name": "img_right", "rect": [3, 460, 146, 28], "offset": [0, 0], "originalSize": [146, 28], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [["1,9729,9729,33071,33071,0,0,1", -1], [2], 0, [], [], []], [[{"name": "light_3", "rect": [173, 385, 81, 31], "offset": [0, 0], "originalSize": [81, 31], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "light_1", "rect": [101, 955, 20, 21], "offset": [0, 0], "originalSize": [20, 21], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "4", "rect": [3, 585, 60, 81], "offset": [0, 0], "originalSize": [60, 81], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[[13, "building_furniture_anim_star"], [20, "building_furniture_anim_star", [-3], [[7, -2, [35], 34]], [16, -1, 0], [5, 300, 300], [-3.295, 88.948, 0, 0, 0, 0, 1, 1, 1, 1]], [55, "shine", 1, [-4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20], [2, "daEXGSj8hKpIgQvdeqbc42", 1, 0], [30, -92.325, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "01", 2, [[1, -21, [0], 1]], [2, "7dk4V8OB5PvprAOD0tCTy/", 1, 0], [5, 52, 52], [97.527, 216.113, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "02", 2, [[1, -22, [2], 3]], [2, "b2RKFnL1dHUJuGY9ctVm2U", 1, 0], [5, 96, 96], [49.264, 121.906, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "03", 2, [[1, -23, [4], 5]], [2, "f88selfQVFcK/lPRc/Zg6W", 1, 0], [5, 96, 96], [-86.736, 180.906, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "04", 2, [[1, -24, [6], 7]], [2, "c8+9qAFWRMb5Q9zQmZtguX", 1, 0], [5, 134, 134], [106.943, 41.849, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "05", 2, [[1, -25, [8], 9]], [2, "80Od3JQwtLMZ5StlTiyTkg", 1, 0], [5, 52, 52], [-118.283, 107.849, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "06", 2, [[1, -26, [10], 11]], [2, "3agyOreylIX4H8M8Yedi5O", 1, 0], [5, 96, 96], [-47.528, 3.605, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "07", 2, [[1, -27, [12], 13]], [2, "bboW8CdZpPMbCTGIyEVlwt", 1, 0], [5, 96, 96], [-149.113, 62.605, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "08", 2, [[1, -28, [14], 15]], [2, "2dfbH+DrdFCYJgTjXFTyrr", 1, 0], [5, 134, 134], [143.509, 164.452, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "09", 2, [[1, -29, [16], 17]], [2, "04GLHD31NOdLlkY9EtkQTT", 1, 0], [5, 96, 96], [49.264, 121.906, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "10", 2, [[1, -30, [18], 19]], [2, "7eXc0tzF1DV7VPKSFFJAb8", 1, 0], [5, 96, 96], [-86.736, 180.906, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "11", 2, [[1, -31, [20], 21]], [2, "3bVnOHojFNhrmLTfyEo9HE", 1, 0], [5, 134, 134], [106.943, 41.849, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "12", 2, [[1, -32, [22], 23]], [2, "ceT4ne+XxP7pLZo8WlFVxE", 1, 0], [5, 96, 96], [49.264, 121.906, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "13", 2, [[1, -33, [24], 25]], [2, "22pSc/VIZOa4Ed/EflSFcG", 1, 0], [5, 96, 96], [-86.736, 180.906, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "14", 2, [[1, -34, [26], 27]], [2, "4dhfv8BYpK3L6h1zZL221/", 1, 0], [5, 134, 134], [106.943, 41.849, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "15", 2, [[1, -35, [28], 29]], [2, "13ANDla/hA1KNAuEv1/SR2", 1, 0], [5, 96, 96], [49.264, 121.906, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "16", 2, [[1, -36, [30], 31]], [2, "42WN4A7y1IPplCCgqjVezp", 1, 0], [5, 96, 96], [-86.736, 180.906, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [3, "17", 2, [[1, -37, [32], 33]], [2, "b4EFT7+c5ExLSjSFryTRqr", 1, 0], [5, 134, 134], [106.943, 41.849, 0, 0, 0, 0, 1, 0.6, 0.6, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, -7, 9, 0, -8, 10, 0, -9, 11, 0, -10, 12, 0, -11, 13, 0, -12, 14, 0, -13, 15, 0, -14, 16, 0, -15, 17, 0, -16, 18, 0, -17, 19, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 6, 1, 37], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 5, -1], [0, 17, 0, 5, 0, 6, 0, 7, 0, 17, 0, 5, 0, 6, 0, 7, 0, 5, 0, 6, 0, 7, 0, 5, 0, 6, 0, 7, 0, 5, 0, 6, 0, 7, 18, 18]], [[[74, "anim_click", 0.3333333333333333, 0, [{}, "paths", 11, [{"par/1": {"props": {"position": [{"frame": 0, "value": [-13.117, 8.662, 0]}, {"frame": 0.3333333333333333, "value": [-39.644, 26.762, 0]}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.3333333333333333, "value": 0}]}}, "par/2": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.3333333333333333, "value": 0}], "position": [{"frame": 0, "value": [0, 0, 0]}, {"frame": 0.3333333333333333, "value": [-15.916, -32.456, 0]}]}}, "par/3": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.3333333333333333, "value": 0}], "position": [{"frame": 0, "value": [5.1, -0.8], "motionPath": []}, {"frame": 0.3333333333333333, "value": [49.4, 8.3], "motionPath": []}]}}, "par/4": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.3333333333333333, "value": 0}], "position": [{"frame": 0, "value": [16.2, -10.7], "motionPath": []}, {"frame": 0.3333333333333333, "value": [45.7, -32.4], "motionPath": []}]}}, "par/5": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.3333333333333333, "value": 0}], "position": [{"frame": 0, "value": [3.3, 8.5], "motionPath": []}, {"frame": 0.3333333333333333, "value": [21.7, 37.2], "motionPath": []}]}}, "par/6": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.3333333333333333, "value": 0}], "position": [{"frame": 0, "value": [-8.8, -2.2], "motionPath": []}, {"frame": 0.31666666666666665, "value": [-38.9, -17.2], "motionPath": []}]}}, "par/7": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.3333333333333333, "value": 0}], "position": [{"frame": 0, "value": [-1.5, -2.2], "motionPath": []}, {"frame": 0.3333333333333333, "value": [18.1, -39.8], "motionPath": []}]}}}, "0", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 255}, {"frame": 0.18333333333333332, "value": 0}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 1, 1]], [{"frame": 0.18333333333333332}, "value", 8, [0, 1.1, 1.1]]], 11, 11]]], "ring1", 11, [{}, "props", 11, [{"opacity": [{"frame": 0.16666666666666666, "value": 220}, {"frame": 0.3333333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 0.4, 0.4]], [{"frame": 0.3333333333333333}, "value", 8, [0, 0.8, 0.8]]], 11, 11]]]]]]], 0, 0, [], [], []], [[{"name": "bg_1", "rect": [3, 3, 289, 201], "offset": [0, 0], "originalSize": [289, 201], "rotated": 1, "capInsets": [56, 89, 54, 81]}], [0], 0, [0], [2], [1]], [[[18, "anim_build_in_a", 0.75, [{"paths": {"narrow": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "img1": {"props": {"opacity": [{"frame": 0, "value": 0}]}}}}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.3, "value": 255}, {"frame": 0.5166666666666667, "value": 255}], "position": [{"frame": 0, "value": [0, -5.6, 0]}, {"frame": 0.3, "value": [0, 10, 0]}, {"frame": 0.4166666666666667, "value": [0, 38, 0]}, {"frame": 0.5166666666666667, "curve": "quadIn", "value": [0, 38, 0]}, {"frame": 0.6666666666666666, "value": [0, 0, 0]}, {"frame": 0.75, "value": [0, 0, 0]}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 0, 0]], [{"frame": 0.3}, "value", 8, [0, 1.2, 0.9]], [{"frame": 0.4166666666666667}, "value", 8, [0, 1, 1]], [{"frame": 0.5166666666666667}, "value", 8, [0, 1, 1]]], 11, 11, 11, 11]]]]], 0, 0, [], [], []], [[{"name": "6", "rect": [67, 3, 58, 82], "offset": [0, 0], "originalSize": [58, 82], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [2]], [[{"name": "bg_di_1", "rect": [0, 0, 20, 20], "offset": [0, 0], "originalSize": [20, 20], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [76]], [["7@34_0,9729,9729,33071,33071,0,0,0", -1], [2], 0, [], [], []], [[[18, "anim_coins_tip", 0.9166666666666666, [{}, "paths", 11, [{"anim": {"props": {"opacity": [{"frame": 0.6666666666666666, "value": 255}, {"frame": 0.9166666666666666, "value": 0}], "y": [{"frame": 0, "value": 0, "curve": "quartOut"}, {"frame": 0.5833333333333334, "value": 100}]}}, "anim/effect/light00": {"props": {"opacity": [{"frame": 0.25, "value": 100}, {"frame": 0.5, "value": 0}]}}, "anim/effect/1": {"props": {"position": [{"frame": 0.13333333333333333, "curve": "quadOut", "value": [-22.493, 11.813, 0]}, {"frame": 0.5166666666666667, "value": [-68.185, 32.065, 0]}], "opacity": [{"frame": 0.5166666666666667, "value": 255}, {"frame": 0.65, "value": 0}]}}, "anim/effect/2": {"props": {"opacity": [{"frame": 0.5166666666666667, "value": 255}, {"frame": 0.65, "value": 0}], "position": [{"frame": 0.13333333333333333, "curve": "quadOut", "value": [4.054, 11.425, 0]}, {"frame": 0.5166666666666667, "value": [32.588, 63.029, 0]}]}}, "anim/effect/3": {"props": {"opacity": [{"frame": 0.5166666666666667, "value": 255}, {"frame": 0.65, "value": 0}], "position": [{"frame": 0.13333333333333333, "curve": "quadOut", "value": [5, 5.2], "motionPath": []}, {"frame": 0.5166666666666667, "value": [68.7, 4.6]}]}}, "anim/effect/4": {"props": {"opacity": [{"frame": 0.5166666666666667, "value": 255}, {"frame": 0.65, "value": 0}], "position": [{"frame": 0.13333333333333333, "curve": "quadOut", "value": [8.108, -13.637, 0]}, {"frame": 0.5166666666666667, "value": [23.285, -64.026, 0]}]}}}, "anim/coin_tip/num", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 0, 0]], [{"frame": 0.16666666666666666}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.25}, "value", 8, [0, 0.5, 0.5]]], 11, 11, 11]]]]]]], 0, 0, [], [], []], [["7@34_0,9729,9729,33071,33071,0,0,0", -1], [2], 0, [], [], []], [[{"name": "img_left", "rect": [155, 472, 145, 28], "offset": [0, 0], "originalSize": [145, 28], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[[29, "anim_build_init", [{"paths": {"narrow": {"props": {"opacity": [{"frame": 0, "value": 0}]}}, "img1": {"props": {"opacity": [{"frame": 0, "value": 0}]}}}}, "props", 11, [{"opacity": [{"frame": 0, "value": 255}], "position": [{"frame": 0, "value": [0, 0, 0]}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 1, 1]]], 11]]]]], 0, 0, [], [], []], [[{"name": "img_load", "rect": [522, 262, 90, 90], "offset": [0, 0], "originalSize": [90, 90], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [3]], [[{"name": "item0", "rect": [522, 175, 131, 85], "offset": [0, 0], "originalSize": [131, 85], "capInsets": [20, 19, 17, 19]}], [0], 0, [0], [2], [3]], [[{"name": "love", "rect": [579, 354, 63, 53], "offset": [0, 0], "originalSize": [63, 53], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [3]], [[{"name": "gray_2", "rect": [116, 554, 54, 26], "offset": [0, 0], "originalSize": [54, 26], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "0circle", "rect": [0, 0, 106, 106], "offset": [0, 0], "originalSize": [106, 106], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [77]]]]