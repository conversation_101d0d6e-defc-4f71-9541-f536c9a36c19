var t = require("../../@babel/runtime/helpers/typeof");

window.__require = (function t(e, o, n) {
    function i(a, p) {
        if (!o[a]) {
            if (!e[a]) {
                var u = (u = a.split("/"))[u.length - 1];
                if (!e[u]) {
                    var c = "function" == typeof __require && __require;
                    if (!p && c) return c(u, !0);
                    if (r) return r(u, !0);
                    throw new Error("Cannot find module '" + a + "'");
                }
                a = u;
            }
            (p = o[a] =
                {
                    exports: {},
                }),
                e[a][0].call(
                    p.exports,
                    function (t) {
                        return i(e[a][1][t] || t);
                    },
                    p,
                    p.exports,
                    t,
                    e,
                    o,
                    n
                );
        }
        return o[a].exports;
    }
    for (var r = "function" == typeof __require && __require, a = 0; a < n.length; a++) i(n[a]);
    return i;
})(
    {
        VolcanoBuffPopwin: [
            function (e, o, n) {
                cc._RF.push(o, "e5cbcMLdZlClp1vgkYdRZok", "VolcanoBuffPopwin");
                o =
                    (this && this.__extends) ||
                    ((i = function (t, e) {
                        return (i =
                            Object.setPrototypeOf ||
                            ({
                                __proto__: [],
                            } instanceof Array &&
                                function (t, e) {
                                    t.__proto__ = e;
                                }) ||
                            function (t, e) {
                                for (var o in e) Object.prototype.hasOwnProperty.call(e, o) && (t[o] = e[o]);
                            })(t, e);
                    }),
                    function (t, e) {
                        function o() {
                            this.constructor = t;
                        }
                        i(t, e), (t.prototype = null === e ? Object.create(e) : ((o.prototype = e.prototype), new o()));
                    });
                var i,
                    r,
                    a,
                    p =
                        (this && this.__decorate) ||
                        function (e, o, n, i) {
                            var r,
                                a = arguments.length,
                                p = a < 3 ? o : null === i ? (i = Object.getOwnPropertyDescriptor(o, n)) : i;
                            if ("object" == ("undefined" == typeof Reflect ? "undefined" : t(Reflect)) && "function" == typeof Reflect.decorate)
                                p = Reflect.decorate(e, o, n, i);
                            else for (var u = e.length - 1; 0 <= u; u--) (r = e[u]) && (p = (a < 3 ? r(p) : 3 < a ? r(o, n, p) : r(o, n)) || p);
                            return 3 < a && p && Object.defineProperty(o, n, p), p;
                        },
                    u =
                        (Object.defineProperty(n, "__esModule", {
                            value: !0,
                        }),
                        e("../../../Script/Audio/AudioMgr")),
                    c = e("../../../Script/Config/Enum"),
                    s = e("../../../Script/Data/DataMgr"),
                    f = e("../../../Script/Event/EventEnum"),
                    l = e("../../../Script/Event/EventMgr"),
                    d = e("../../../Script/Manager/OnlineMgr"),
                    g = e("../../../Script/Manager/SubpackageMgr"),
                    h = e("../../../Script/SDK/TA/TALog"),
                    _ = e("../../../Script/Utils/DateUtils"),
                    y = ((e = e("../../../Script/View/base/BaseAnimPopwin")), (b = cc._decorator).ccclass),
                    b = b.property;
                o(v, (r = e.default)),
                    ((a = v).prototype.onOpen = function (t) {
                        r.prototype.onOpen.call(this, t),
                            (this.islock = !0),
                            (this.ids = t.data.ids),
                            (this.isPop = (null == (t = null == t ? void 0 : t.data) ? void 0 : t.ispop) || !1),
                            (this.id = this.ids[0]),
                            (this.data = s.DataMgr.Buff.getBuffDataByID(this.id)),
                            this.initUI(),
                            this.ids.splice(0, 1),
                            h.default.I.track("buff_panel_show", {
                                id: this.id,
                                time: this.data.endtime - _.default.now,
                            });
                    }),
                    (v.prototype.onOpend = function () {
                        r.prototype.onOpend.call(this), (this.islock = !1);
                    }),
                    (v.prototype.onTouchMask = function () {
                        this.onBack();
                    }),
                    (v.prototype.onClose = function () {
                        r.prototype.onClose.call(this);
                    }),
                    (v.prototype.onClosed = function () {
                        l.EventMgr.emit(f.EventEnum.AddBuff, {
                            id: this.id,
                        }),
                            this.isPop &&
                                l.EventMgr.emit(f.EventEnum.RefreshBuff, {
                                    id: this.id,
                                }),
                            g.default.I.releaseBundle(g.AssetBundleName.VolcanoBuff),
                            r.prototype.onClosed.call(this);
                    }),
                    (v.prototype.update = function () {
                        this.data && (this.txttime.string = _.default.getFormatBySecond9(this.data.endtime - _.default.now));
                    }),
                    Object.defineProperty(v.prototype, "isRelease", {
                        get: function () {
                            return !0;
                        },
                        enumerable: !1,
                        configurable: !0,
                    }),
                    Object.defineProperty(v.prototype, "BannerOpen", {
                        get: function () {
                            return d.OnlineMgr.getBanner(this.prefabPath);
                        },
                        enumerable: !1,
                        configurable: !0,
                    }),
                    (v.prototype.initUI = function () {
                        var t = "state_ep_open_8";
                        if (114 == this.id)
                            switch (s.DataMgr.Buff.NBuffMaxValue + 2) {
                                case c.DoubleEnergyState.Quad:
                                    t = "state_ep_open_4";
                                    break;

                                case c.DoubleEnergyState.Eight:
                                    t = "state_ep_open_8";
                                    break;

                                case c.DoubleEnergyState.Sixteen:
                                    t = "state_ep_open_16";
                                    break;

                                case c.DoubleEnergyState.ThirtyTwo:
                                    t = "state_ep_open_32";
                                    break;

                                default:
                                    t = "state_ep_ok";
                            }
                        this.playAni(this.nodeBtnAni, t);
                    }),
                    (v.prototype.onBack = function () {
                        u.AudioMgr.button(),
                            this.islock ||
                                (-1 < s.DataMgr.Config.getBuffyId(this.id).action_object.indexOf(c.BuffActObjType.EnergyIndex) &&
                                    l.EventMgr.emit(f.EventEnum.EnergyStateChange),
                                h.default.I.track("buff_energy_close_tap"),
                                this.doClose(a, !1));
                    }),
                    (v.prototype.onQuad = function () {
                        var t;
                        u.AudioMgr.button(),
                            this.islock ||
                                (114 == this.id
                                    ? (t = s.DataMgr.Buff.NBuffMaxValue + 2) > c.DoubleEnergyState.Double && s.DataMgr.User.reqSetDoubleEnergyState(t)
                                    : s.DataMgr.User.reqSetDoubleEnergyState(c.DoubleEnergyState.Eight),
                                h.default.I.track("buff_energy_button_tap"),
                                this.doClose(a, !1));
                    }),
                    p([b(cc.Label)], v.prototype, "txttime", void 0),
                    p([b(cc.Node)], v.prototype, "nodeBtnAni", void 0),
                    (e = a = p([y], v));
                function v() {
                    var t = (null !== r && r.apply(this, arguments)) || this;
                    return (
                        (t.prefabPath = "./prefab/popwin_buff_volcano"),
                        (t.ids = []),
                        (t.id = 0),
                        (t.txttime = null),
                        (t.nodeBtnAni = null),
                        (t.data = null),
                        (t.islock = !1),
                        (t.isPop = !1),
                        t
                    );
                }
                (n.default = e), cc._RF.pop();
            },
            {
                "../../../Script/Audio/AudioMgr": void 0,
                "../../../Script/Config/Enum": void 0,
                "../../../Script/Data/DataMgr": void 0,
                "../../../Script/Event/EventEnum": void 0,
                "../../../Script/Event/EventMgr": void 0,
                "../../../Script/Manager/OnlineMgr": void 0,
                "../../../Script/Manager/SubpackageMgr": void 0,
                "../../../Script/SDK/TA/TALog": void 0,
                "../../../Script/Utils/DateUtils": void 0,
                "../../../Script/View/base/BaseAnimPopwin": void 0,
            },
        ],
    },
    {},
    ["VolcanoBuffPopwin"]
);
