	define("__plugin__/wxbd990766293b9dc4", function(require, module, exports){			
module.exports = require('wxbd990766293b9dc4/index.js'); 
 			});
 		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/Objectentries.js", function(require, module, exports){ 			
Object.entries||(Object.entries=function(e){for(var r=Object.keys(e),t=r.length,n=new Array(t);t--;)n[t]=[r[t],e[r[t]]];return n}); 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/Objectvalues.js", function(require, module, exports){ 			
Object.values||(Object.values=function(e){if(e!==Object(e))throw new TypeError("Object.values called on a non-object");var t,r=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.push(e[t]);return r}); 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/arrayLikeToArray.js", function(require, module, exports){ 			
function _arrayLikeToArray(r,a){(null==a||a>r.length)&&(a=r.length);for(var e=0,n=new Array(a);e<a;e++)n[e]=r[e];return n}module.exports=_arrayLikeToArray; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/arrayWithHoles.js", function(require, module, exports){ 			
function _arrayWithHoles(r){if(Array.isArray(r))return r}module.exports=_arrayWithHoles; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/arrayWithoutHoles.js", function(require, module, exports){ 			
var arrayLikeToArray=require("./arrayLikeToArray");function _arrayWithoutHoles(r){if(Array.isArray(r))return arrayLikeToArray(r)}module.exports=_arrayWithoutHoles; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/assertThisInitialized.js", function(require, module, exports){ 			
function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}module.exports=_assertThisInitialized; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/classCallCheck.js", function(require, module, exports){ 			
function _classCallCheck(a,l){if(!(a instanceof l))throw new TypeError("Cannot call a class as a function")}module.exports=_classCallCheck; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/createClass.js", function(require, module, exports){ 			
var toPropertyKey=require("./toPropertyKey");function _defineProperties(e,r){for(var t=0;t<r.length;t++){var o=r[t];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,toPropertyKey(o.key),o)}}function _createClass(e,r,t){return r&&_defineProperties(e.prototype,r),t&&_defineProperties(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e}module.exports=_createClass; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/createSuper.js", function(require, module, exports){ 			
var getPrototypeOf=require("./getPrototypeOf"),isNativeReflectConstruct=require("./isNativeReflectConstruct"),possibleConstructorReturn=require("./possibleConstructorReturn");function _createSuper(t){var e=isNativeReflectConstruct();return function(){var r,o=getPrototypeOf(t);if(e){var s=getPrototypeOf(this).constructor;r=Reflect.construct(o,arguments,s)}else r=o.apply(this,arguments);return possibleConstructorReturn(this,r)}}module.exports=_createSuper; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/defineProperty.js", function(require, module, exports){ 			
var toPropertyKey=require("./toPropertyKey");function _defineProperty(e,r,t){return(r=toPropertyKey(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}module.exports=_defineProperty; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/get.js", function(require, module, exports){ 			
var superPropBase=require("./superPropBase");function _get(){return"undefined"!=typeof Reflect&&Reflect.get?module.exports=_get=Reflect.get.bind():module.exports=_get=function(e,t,r){var o=superPropBase(e,t);if(o){var p=Object.getOwnPropertyDescriptor(o,t);return p.get?p.get.call(arguments.length<3?e:r):p.value}},_get.apply(this,arguments)}module.exports=_get; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/getPrototypeOf.js", function(require, module, exports){ 			
function _getPrototypeOf(t){return module.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},_getPrototypeOf(t)}module.exports=_getPrototypeOf; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/inherits.js", function(require, module, exports){ 			
var setPrototypeOf=require("./setPrototypeOf");function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&setPrototypeOf(e,t)}module.exports=_inherits; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/isNativeReflectConstruct.js", function(require, module, exports){ 			
function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}module.exports=_isNativeReflectConstruct; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/iterableToArray.js", function(require, module, exports){ 			
function _iterableToArray(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}module.exports=_iterableToArray; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/iterableToArrayLimit.js", function(require, module, exports){ 			
function _iterableToArrayLimit(r,e){var l=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=l){var t,n,i,a,u=[],o=!0,f=!1;try{if(i=(l=l.call(r)).next,0===e){if(Object(l)!==l)return;o=!1}else for(;!(o=(t=i.call(l)).done)&&(u.push(t.value),u.length!==e);o=!0);}catch(r){f=!0,n=r}finally{try{if(!o&&null!=l.return&&(a=l.return(),Object(a)!==a))return}finally{if(f)throw n}}return u}}module.exports=_iterableToArrayLimit; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/nonIterableRest.js", function(require, module, exports){ 			
function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}module.exports=_nonIterableRest; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/nonIterableSpread.js", function(require, module, exports){ 			
function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}module.exports=_nonIterableSpread; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/objectSpread2.js", function(require, module, exports){ 			
var defineProperty=require("./defineProperty");function ownKeys(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);r&&(o=o.filter((function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable}))),t.push.apply(t,o)}return t}function _objectSpread2(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?ownKeys(Object(t),!0).forEach((function(r){defineProperty(e,r,t[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ownKeys(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}))}return e}module.exports=_objectSpread2; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/possibleConstructorReturn.js", function(require, module, exports){ 			
var _typeof=require("./typeof"),assertThisInitialized=require("./assertThisInitialized");function _possibleConstructorReturn(e,r){if(r&&("object"===_typeof(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return assertThisInitialized(e)}module.exports=_possibleConstructorReturn; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/setPrototypeOf.js", function(require, module, exports){ 			
function _setPrototypeOf(t,e){return module.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},_setPrototypeOf(t,e)}module.exports=_setPrototypeOf; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/slicedToArray.js", function(require, module, exports){ 			
var arrayWithHoles=require("./arrayWithHoles"),iterableToArrayLimit=require("./iterableToArrayLimit"),unsupportedIterableToArray=require("./unsupportedIterableToArray"),nonIterableRest=require("./nonIterableRest");function _slicedToArray(r,e){return arrayWithHoles(r)||iterableToArrayLimit(r,e)||unsupportedIterableToArray(r,e)||nonIterableRest()}module.exports=_slicedToArray; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/superPropBase.js", function(require, module, exports){ 			
var getPrototypeOf=require("./getPrototypeOf");function _superPropBase(e,r){for(;!Object.prototype.hasOwnProperty.call(e,r)&&null!==(e=getPrototypeOf(e)););return e}module.exports=_superPropBase; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/toConsumableArray.js", function(require, module, exports){ 			
var arrayWithoutHoles=require("./arrayWithoutHoles"),iterableToArray=require("./iterableToArray"),unsupportedIterableToArray=require("./unsupportedIterableToArray"),nonIterableSpread=require("./nonIterableSpread");function _toConsumableArray(r){return arrayWithoutHoles(r)||iterableToArray(r)||unsupportedIterableToArray(r)||nonIterableSpread()}module.exports=_toConsumableArray; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/toPrimitive.js", function(require, module, exports){ 			
var _typeof=require("./typeof");function _toPrimitive(r,t){if("object"!==_typeof(r)||null===r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var i=e.call(r,t||"default");if("object"!==_typeof(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}module.exports=_toPrimitive; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/toPropertyKey.js", function(require, module, exports){ 			
var _typeof=require("./typeof"),toPrimitive=require("./toPrimitive");function _toPropertyKey(r){var t=toPrimitive(r,"string");return"symbol"===_typeof(t)?t:String(t)}module.exports=_toPropertyKey; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/typeof.js", function(require, module, exports){ 			
function _typeof(o){return module.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(o){return typeof o}:function(o){return o&&"function"==typeof Symbol&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},_typeof(o)}module.exports=_typeof; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/@babel/runtime/helpers/unsupportedIterableToArray.js", function(require, module, exports){ 			
var arrayLikeToArray=require("./arrayLikeToArray");function _unsupportedIterableToArray(r,e){if(r){if("string"==typeof r)return arrayLikeToArray(r,e);var t=Object.prototype.toString.call(r).slice(8,-1);return"Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t?Array.from(r):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?arrayLikeToArray(r,e):void 0}}module.exports=_unsupportedIterableToArray; 
 			}); 
		define("__plugin__/wxbd990766293b9dc4/index.js", function(require, module, exports){ 			
"use strict";var e=require("@babel/runtime/helpers/get"),t=require("@babel/runtime/helpers/getPrototypeOf"),n=require("@babel/runtime/helpers/objectSpread2");require("@babel/runtime/helpers/Objectvalues");var i=require("@babel/runtime/helpers/toConsumableArray"),r=require("@babel/runtime/helpers/inherits"),o=require("@babel/runtime/helpers/createSuper"),s=require("@babel/runtime/helpers/typeof"),a=require("@babel/runtime/helpers/slicedToArray");require("@babel/runtime/helpers/Objectentries");var u=require("@babel/runtime/helpers/classCallCheck"),c=require("@babel/runtime/helpers/createClass"),l=require("@babel/runtime/helpers/defineProperty");!function(){var h={388:function(e,t,n){var i;n.d(t,{QD:function(){return r},C2:function(){return o},ZP:function(){return d},Mi:function(){return f}});var r,o,s=n(342);!function(e){e.STRING="STRING",e.LONGLONG="LONGLONG",e.UINT="UINT",e.INT="INT"}(r||(r={})),function(e){e.SvrReport="SvrReport",e.KvStat="KvStat"}(o||(o={}));var h=(l(i={},r.STRING,(function(e){return"string"==typeof e&&e.length<=s.I})),l(i,r.LONGLONG,(function(e){return"number"==typeof e&&Math.floor(e)===e})),l(i,r.UINT,(function(e){return"number"==typeof e&&Math.floor(e)===e&&e>=0})),l(i,r.INT,(function(e){return"number"==typeof e&&Math.floor(e)===e})),i),d=function(){function e(t){var n=t.logid,i=t.schemas,r=void 0===i?{}:i,o=t.base,s=t.debug,a=void 0!==s&&s,c=t.namespace,l=void 0===c?"":c,h=t.skipValidateInProdEnv,d=void 0===h||h;u(this,e),this.debug=!1,this.base={},this.namespace="",this.skipValidateInProdEnv=!0,this.asyncGetBase=function(){return Promise.resolve({})},this.logid=n,this.schemas=r,o&&this.setBase(o),this.debug=a,this.skipValidateInProdEnv=d,this.namespace=l?" ".concat(l," "):" "}return c(e,[{key:"setBase",value:function(e){this.validate(e),this.base=Object.assign(Object.assign({},this.base),e)}},{key:"validate",value:function(e){if((this.debug||!this.skipValidateInProdEnv)&&Object.keys(this.schemas).length)for(var t=0,n=Object.entries(e);t<n.length;t++){var i=a(n[t],2),r=i[0],o=i[1];if(void 0!==o){var s=this.schemas[r];s&&this.validateColumn(s,o)}}}},{key:"validateColumn",value:function(e,t){return h[e](t)}}]),e}(),g="";function f(e,t,n){g?e&&e(g):(t({success:function(t){g=t.networkType,e&&e(g)}}),n((function(t){g=t.networkType,t.isConnected||(g="none"),e&&e(g)})))}},342:function(e,t,n){n.d(t,{I:function(){return i}});var i=8192},512:function(e,t,n){function i(){}function r(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20,t=arguments.length>1?arguments[1]:void 0,n=(t?"":"!#%()*+,-./:;=?@[]^_`{|}~")+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",i=n.length,r=[],o=0;o<e;o++)r[o]=n.charAt(Math.random()*i);return r.join("")}function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"0.0.0",t=arguments.length>1?arguments[1]:void 0;if("string"!=typeof e||"string"!=typeof t)return"string"!=typeof e&&"string"!=typeof t?0:"string"!=typeof e?1:-1;for(var n=e.split("."),i=t.split("."),r=Math.max(n.length,i.length);n.length<r;)n.push("0");for(;i.length<r;)i.push("0");for(var o=0;o<r;o++){var s=parseInt(n[o],10),a=parseInt(i[o],10);if(s>a)return 1;if(s<a)return-1}return 0}function a(e){return"number"==typeof e&&isFinite(e)}function u(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return!e;if(e instanceof Error)return""===e.message;switch(Object.prototype.toString.call(e)){case"[object String]":case"[object Array]":return!e.length;case"[object File]":case"[object Map]":case"[object Set]":return!e.size;case"[object Object]":return!Object.keys(e).length}return!1}function c(e){return"[object String]"===Object.prototype.toString.call(e)}function l(e){return"[object Object]"===Object.prototype.toString.call(e)}function h(e){return e&&"[object Function]"===Object.prototype.toString.call(e)}function d(e){var t=e;try{"object"==s(e)&&(t="string"==typeof e.errMsg?e.errMsg:"string"==typeof e.errmsg?e.errmsg:"string"==typeof e.message?e.message:JSON.stringify(e))}catch(e){}return t}function g(e,t,n){if(!n)return e;var i=e,r=new RegExp("([?&])".concat(t,"=.*?(&|$)"),"i");if(i.match(r))return i.replace(r,"$1".concat(t,"=").concat(n,"$2"));var o=-1!==i.indexOf("?")?"&":"?",s=i.split("#");return i="".concat(s[0]+o+t,"=").concat(n),s[1]&&(i+="#".concat(s[1])),i}n.d(t,{YP:function(){return i},hQ:function(){return r},yC:function(){return o},hj:function(){return a},xb:function(){return u},HD:function(){return c},Kn:function(){return l},mf:function(){return h},bV:function(){return d},vr:function(){return g}})},580:function(e,t,n){var i,r,o,s,a;n.d(t,{t4:function(){return i},dg:function(){return s},vm:function(){return a}}),function(e){e.devtools="devtools",e.android="android",e.ios="ios",e.windows="windows",e.mac="mac"}(i||(i={})),function(e){e.portrait="portrait",e.landscape="landscape",e.landscapeLeft="landscapeLeft",e.landscapeRight="landscapeRight"}(r||(r={})),function(e){e.DEVELOP="develop",e.TRIAL="trial",e.RELEASE="release"}(o||(o={})),function(e){e[e.release=1]="release",e[e.develop=2]="develop",e[e.trial=3]="trial"}(s||(s={})),function(e){e[e.Android=0]="Android",e[e.IOSJscore=1]="IOSJscore",e[e.IOSWK=2]="IOSWK",e[e.UnKnow=3]="UnKnow",e[e.PC=4]="PC",e[e.Devtools=5]="Devtools",e[e.IPadJscore=6]="IPadJscore",e[e.IPadWK=7]="IPadWK",e[e.IOSWKPlus=8]="IOSWKPlus",e[e.IPadWKPlus=9]="IPadWKPlus"}(a||(a={}))},662:function(e,t,n){n.d(t,{Z:function(){return I}});var i,r,o,s,a,u=n(512),c=n(974),l=n(580),h="undefined"!=typeof mb&&void 0!==mb.env,d=!("undefined"==typeof pluginEnv),g=!h&&"undefined"!=typeof wx&&"undefined"!=typeof Page,f=!(h||"undefined"==typeof wx||g)||d,v={isMagicBrush:h,isMiniGamePlugin:d,isMiniGame:f,isMiniProgram:g,isIOS:!1,isAndroid:!1,isDevtools:!1,isWindows:!1,isMac:!1,isPC:!1,isWK:!1,isWKPlus:!1,isIPad:!1,systemInfo:{},accountInfo:{},wx:{},runtimeType:l.vm.UnKnow,pluginWx:{},pluginAppId:"",pluginVersion:""};function p(e){var t;if(v.systemInfo=e,v.systemInfo.screenWidth||v.systemInfo.screenWidth||(v.systemInfo.screenWidth=0,v.systemInfo.screenHeight=0),v.runtimeType=(0,c.$)(v.systemInfo),m(v.systemInfo),(0,u.yC)(v.systemInfo.SDKVersion,"2.24.4")>=0&&(null===(t=v.pluginWx)||void 0===t?void 0:t.getAccountInfoSync))try{var n=(v.pluginWx.getAccountInfoSync()||{}).plugin,i=void 0===n?{}:n;v.pluginAppId=i.appId,v.pluginVersion=i.version}catch(e){}}function m(e){var t,i=null===(t=e.platform)||void 0===t?void 0:t.toLocaleLowerCase();i===l.t4.devtools?v.isDevtools=!0:i===l.t4.ios?(v.isIOS=!0,v.isWK=!!(null===n.g||void 0===n.g?void 0:n.g.isIOSHighPerformanceMode),v.isWKPlus=!!(null===n.g||void 0===n.g?void 0:n.g.isIOSHighPerformanceModePlus)):i===l.t4.android?v.isAndroid=!0:i===l.t4.windows?v.isWindows=!0:i===l.t4.mac?v.isMac=!0:void 0===i&&(v.isIOS=!0),(v.isWindows||v.isMac)&&(v.isPC=!0),e.model&&e.model.indexOf("iPad")>-1&&(v.isIPad=!0)}if(f||g){if(d){var y=pluginEnv.customEnv;y&&y.customEnv&&(pluginEnv.customEnv=y.customEnv),"undefined"!=typeof wx&&(v.pluginWx=wx),pluginEnv.customEnv?v.wx=pluginEnv.customEnv.wx?pluginEnv.customEnv.wx:v.pluginWx:v.wx=v.pluginWx}else v.wx=wx;if(v.wx){try{p(v.wx.getSystemInfoSync()),v.wx.getSystemInfo({success:function(e){p(e)}})}catch(e){}try{v.accountInfo=null!==(o=null===(r=(i=v.wx).getAccountInfoSync)||void 0===r?void 0:r.call(i))&&void 0!==o?o:{}}catch(e){}}}else h&&mb.JSBridge.invoke("getSystemInfoSync",{},(function(e){v.systemInfo=e,v.runtimeType=(0,c.$)(v.systemInfo),m(v.systemInfo)}));null===(a=(s=v.wx).onDeviceOrientationChange)||void 0===a||a.call(s,(function(){v.wx.getSystemInfo({success:function(e){v.systemInfo=e||{}}})})),d&&(v.pluginEnv=pluginEnv);var I=v},54:function(e,t,n){n.d(t,{Z:function(){return a}});var i=n(388),s={19283:"GameComponent",19335:"GameWhatsNew",20285:"MidasFriendPayment",21494:"WXGameWASMLaunchClose",21493:"WXGameWASMLaunchException",21492:"WXGameWASMLaunchTime",21898:"GameLifeMiniGameCardAction",20267:"GameAdsSkipCard"},a=function(e){r(n,e);var t=o(n);function n(e){var i,r=e.reportFunc,o=e.logid,a=e.protocolParams,c=e.schemas,l=e.base,h=e.asyncGetBase,d=void 0===h?function(){return Promise.resolve({})}:h,g=e.debug,f=e.namespace,v=void 0===f?"":f,p=e.skipValidateInProdEnv,m=void 0===p||p;u(this,n);var y=o;return"number"==typeof o&&s[o]&&(y=s[o]),(i=t.call(this,{logid:y,schemas:c,base:l,debug:g,namespace:v,skipValidateInProdEnv:m})).asyncGetBase=d,i.reportFunc=r||function(){},i.protocolParams=a,i.sendCbk=null,i}return c(n,[{key:"setAppInfoBase",value:function(e){Object.assign(this.schemas,{AppId:i.QD.STRING,AppVersion:i.QD.UINT,AppState:i.QD.UINT}),this.protocolParams.unshift("AppId","AppVersion","AppState"),this.setBase(e)}},{key:"send",value:function(e,t){var n,i=this;n=Array.isArray(e)?e:[e],this.asyncGetBase().then((function(e){i.setBase(e),n.forEach((function(e){var n=Object.assign(Object.assign({},i.base),e);i.validate(n);var r=[];i.protocolParams.forEach((function(e){var t=void 0!==n[e]?n[e]:"";r.push(t)})),i.reportFunc({key:i.logid,value:r.map(encodeURIComponent).join(","),immediately:!0});var o=t||i.sendCbk;o&&o(i.logid,r.map(encodeURIComponent).join(",")),i.debug&&(n.IsError?console.error:console.log)("[minigamefe".concat(i.namespace,"keyValueReport]: ").concat(i.logid),n)}))}))}}]),n}(i.ZP)},431:function(e,t,n){n.r(t),n.d(t,{default:function(){return re}});var i={};n.r(i),n.d(i,{navigateToMiniProgram:function(){return h}});var l=n(662);function h(e){l.Z.wx.navigateToMiniProgram&&(l.Z.pluginEnv&&l.Z.pluginEnv.navigateToMiniProgramDirectly?l.Z.pluginEnv.navigateToMiniProgramDirectly({appId:e}):l.Z.wx.navigateToMiniProgram({appId:e}))}JSON.parse;var d,g=JSON.stringify,f=(Object.keys,String),v="object",p=function(e,t){return t},m=function(e,t,n){var i=f(t.push(n)-1);return e.set(n,i),i};!function(e){e.log="log",e.info="info",e.warn="warn",e.error="error"}(d||(d={}));var y,I,T,S,b=function(){function e(t){var n=t.reportFunc,i=t.base,r=t.debug,o=t.delimiter,s=t.namespace;u(this,e),this.reportFunc=null,this.debug=!1,this.base={},this.delimiter=" ",this.namespace="",n&&(this.reportFunc=n,i&&this.setBase(i),this.debug=!!r,o&&(this.delimiter=o),this.namespace=s?" ".concat(s," "):" ")}return c(e,[{key:"setBase",value:function(e){this.base=Object.assign(Object.assign({},this.base),e)}},{key:"log",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this.report(d.log,t)}},{key:"info",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this.report(d.info,t)}},{key:"warn",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this.report(d.warn,t)}},{key:"error",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this.report(d.error,t)}},{key:"getLogStr",value:function(e,t){var n,i=this;if(!this.reportFunc)return"";if(!t)return"";var r="";t.forEach((function(e){r.length>0&&(r+=i.delimiter);var t=e;"object"==s(t)&&(t=t instanceof TypeError?t.message:function(e,t,n){for(var i=t&&s(t)===v?function(e,n){return""===e||-1<t.indexOf(e)?n:void 0}:t||p,r=new Map,o=[],a=[],u=+m(r,o,i.call({"":e},"",e)),c=!u;u<o.length;)c=!0,a[u]=g(o[u++],l,n);return"["+a.join(",")+"]";function l(e,t){if(c)return c=!c,t;var n=i.call(this,e,t);switch(s(n)){case v:if(null===n)return n;case"string":return r.get(n)||m(r,o,n)}return n}}(t)),r+="".concat(String(t).trim())}));var o="[minigamefe".concat(this.namespace,"XLog]: ").concat(JSON.stringify(Object.assign(Object.assign({},this.base),{type:e,content:r})));return this.debug&&(null===(n=console[e])||void 0===n||n.call(console,o)),o}},{key:"report",value:function(e,t){var n=this.reportFunc;if(n&&n[e]&&"function"==typeof n[e]){var i=this.getLogStr(e,t);i&&n[e](i)}}}]),e}(),x=n(512);!function(e){e[e.Front=1]="Front",e[e.Background=2]="Background"}(y||(y={})),function(e){e[e.Default=0]="Default",e[e.GameTransfer=1]="GameTransfer",e[e.Request=2]="Request",e[e.DownloadFile=3]="DownloadFile",e[e.CgiSpeedMeasure=4]="CgiSpeedMeasure",e[e.BadJs=5]="BadJs",e[e.Init=6]="Init",e[e.CostTime=7]="CostTime",e[e.Error=8]="Error",e[e.UploadFile=9]="UploadFile",e[e.Login=10]="Login",e[e.WebTransfer=11]="WebTransfer"}(I||(I={})),function(e){e[e.Info=101]="Info",e[e.Warn=102]="Warn",e[e.Error=103]="Error"}(T||(T={})),function(e){e.GameTransferReport="GameTransferReport",e.KeyValueReport="KeyValueReport",e.WxRequestReport="WxRequestReport"}(S||(S={}));var w,k,P=Object.values(I).filter((function(e){return(0,x.hj)(e)})),R=new(function(){function e(){u(this,e),this.options={debug:!1,logReport:!1,qualityReportType:S.KeyValueReport,pluginAppId:"",pluginVersion:"",xlogName:"minigamefe"}}return c(e,[{key:"set",value:function(e){Object.assign(this.options,e)}},{key:"get",value:function(e){return this.options[e]}}]),e}()),C=function(){function e(t){u(this,e),this.identifier="".concat(R.get("pluginAppId")||l.Z.pluginAppId,"/plugin.js"),this.quality=t,this.init()}return c(e,[{key:"init",value:function(){var e,t,n=this;null===(t=null===(e=l.Z.wx)||void 0===e?void 0:e.onError)||void 0===t||t.call(e,(function(e){n.processError(e.message)}))}},{key:"processError",value:function(e){var t;if(e.indexOf(this.identifier)>-1){var n=e.split("\n"),i=n[0],r=null===(t=n[2])||void 0===t?void 0:t.split(":")[0];this.quality.innerReport({Type:I.BadJs,Target:i,CostTime:0,Result:e,CustomKey1:r})}}}]),e}(),D=n(279);!function(e){e.GAMETRANSFER_IS_NOT_EXIST="gameTransfer is not exist",e.REQUEST_IS_NOT_EXIST="request is not exist",e.UPLOADFILE_IS_NOT_EXIST="uploadFile is not exist",e.LOGIN_IS_NOT_EXIST="login is not exist",e.URL_IS_EMPTY="request url is empty.",e.HEADER_INVALID="request header is invalid",e.METHOD_IS_NOT_EXIST="method is not exist"}(w||(w={}));var E=function(e){r(n,e);var t=o(n);function n(){return u(this,n),t.apply(this,arguments)}return c(n)}(D.TinyEmitter);!function(e){e.login="login",e.gameTransfer="gameTransfer",e.request="request",e.uploadFile="uploadFile",e.downloadFile="downloadFile",e.webTransfer="webTransfer"}(k||(k={}));var N={QualityReportType:S,RequestRejectType:w,ReportType:I,PluginReportType:T},A=n(388),M=n(580),O=function(e){r(n,e);var t=o(n);function n(e){var i,r=e.reportFunc,o=e.logid,s=e.schemas,a=void 0===s?{}:s,c=e.base,l=e.reportType,h=e.asyncGetBase,d=void 0===h?function(){return Promise.resolve({})}:h,g=e.debug,f=e.namespace,v=void 0===f?"":f,p=e.skipValidateInProdEnv,m=void 0===p||p;return u(this,n),(i=t.call(this,{logid:o,schemas:a,base:c,debug:g,namespace:v,skipValidateInProdEnv:m})).reportType=A.C2.SvrReport,i.asyncGetBase=d,i.reportFunc=r,void 0!==l&&(i.reportType=l),i}return c(n,[{key:"setKvStatBase",value:function(e,t){if(this.reportType===A.C2.KvStat){var n=(e.system||"").split(" ");Object.assign(this.schemas,{DeviceModel:A.QD.STRING,DeviceBrand:A.QD.STRING,OsName:A.QD.STRING,OsVersion:A.QD.STRING,LanguageVersion:A.QD.STRING,Count:A.QD.UINT,AppId:A.QD.STRING,AppVersion:A.QD.UINT,AppState:A.QD.UINT}),this.setBase({DeviceModel:e.model||"",DeviceBrand:e.brand||"",OsName:n[0]||"",OsVersion:n[1]||"",LanguageVersion:e.language||"",Count:1}),(null==t?void 0:t.miniProgram)&&this.setBase({AppState:M.dg[t.miniProgram.envVersion]||M.dg.release,AppId:t.miniProgram.appId,AppVersion:0})}}},{key:"send",value:function(e,t){var n=this;if(this.reportFunc&&"function"==typeof this.reportFunc){var i,r=[],o=this.logid;i=Array.isArray(e)?e:[e],this.asyncGetBase().then((function(e){n.setBase(e),i.forEach((function(e){var t=Object.assign(Object.assign({},n.base),e);if(t.ExternInfo&&"object"==s(t.ExternInfo)&&n.schemas.ExternInfo===A.QD.STRING&&(t.ExternInfo=encodeURIComponent(JSON.stringify(t.ExternInfo))),n.validate(t),n.reportType===A.C2.KvStat){var i=Math.floor(Date.now()/1e3);Object.assign(t,{StartTime:i,EndTime:i})}var o={log_id:n.logid,custom_data:t};r.push(o),n.debug}));var a={report_list:r};n.reportFunc({req_path:"comm_datareport",json_data:JSON.stringify(a),success:function(){t&&t(o,JSON.stringify(a))},fail:function(e){t&&t(o,(0,x.bV)(e))}})}))}}}]),n}(A.ZP),L=n(54),F="https://game.weixin.qq.com",_="minigamefe_session_id",B=function(e){r(n,e);var t=o(n);function n(e){var i,r=e.reportFunc,o=e.logid,s=e.schemas,a=void 0===s?{}:s,c=e.base,l=e.debug,h=e.namespace,d=e.asyncGetBase,g=void 0===d?function(){return Promise.resolve({})}:d,f=e.reportType,v=e.skipValidateInProdEnv,p=void 0===v||v;return u(this,n),(i=t.call(this,{logid:o,schemas:a,base:c,debug:l,namespace:h,skipValidateInProdEnv:p})).reportType=A.C2.SvrReport,i.reportFunc=r,i.asyncGetBase=g,void 0!==f&&(i.reportType=f),i}return c(n,[{key:"setKvStatBase",value:function(e,t){if(this.reportType===A.C2.KvStat){var n=(e.system||"").split(" ");Object.assign(this.schemas,{DeviceModel:A.QD.STRING,DeviceBrand:A.QD.STRING,OsName:A.QD.STRING,OsVersion:A.QD.STRING,LanguageVersion:A.QD.STRING,Count:A.QD.UINT,AppId:A.QD.STRING,AppVersion:A.QD.UINT,AppState:A.QD.UINT}),this.setBase({DeviceModel:e.model||"",DeviceBrand:e.brand||"",OsName:n[0]||"",OsVersion:n[1]||"",LanguageVersion:e.language||"",Count:1}),(null==t?void 0:t.miniProgram)&&this.setBase({AppState:M.dg[t.miniProgram.envVersion]||M.dg.release,AppId:t.miniProgram.appId,AppVersion:0})}}},{key:"send",value:function(e,t){var n=this;if(this.reportFunc&&"function"==typeof this.reportFunc){var i,r=[],o=this.logid;i=Array.isArray(e)?e:[e],this.asyncGetBase().then((function(e){n.setBase(e),i.forEach((function(e){var t=Object.assign(Object.assign({},n.base),e);n.validate(t);var i={log_id:o,custom_data:t};r.push(i)}));var s=JSON.stringify({report_list:r});n.debug;var a="".concat(F,"/cgi-bin/comm/datareport");n.reportFunc({url:a,method:"POST",header:{"content-type":"application/x-www-form-urlencoded"},data:s,success:function(){t&&t(o,s)},fail:function(e){t&&t(o,(0,x.bV)(e))}})}))}}}]),n}(A.ZP),W=["BenchmarkLevel","NetworkType","RuntimeType","PluginAppId","PluginVersion","Scene","SDKVersion","IsVisible","Type","Target","Params","Result","CostTime","ExternInfo","CustomKey1","CustomKey2","CustomKey3","IsError","FELibVersion","Query"],G={BenchmarkLevel:A.QD.UINT,NetworkType:A.QD.STRING,RuntimeType:A.QD.UINT,PluginAppId:A.QD.STRING,PluginVersion:A.QD.STRING,Scene:A.QD.UINT,SDKVersion:A.QD.STRING,IsVisible:A.QD.UINT,Type:A.QD.UINT,Target:A.QD.STRING,Params:A.QD.STRING,Result:A.QD.STRING,CostTime:A.QD.UINT,ExternInfo:A.QD.STRING,CustomKey1:A.QD.STRING,CustomKey2:A.QD.STRING,CustomKey3:A.QD.STRING,IsError:A.QD.UINT,FELibVersion:A.QD.STRING,Query:A.QD.STRING},V=n(974),q="1.1.89",K=n(342);function j(e,t){var n=e||"";if((0,x.Kn)(n))try{n=JSON.stringify(n)}catch(e){n=""}else n=String(n);return n.length&&n.length>=K.I&&(n=n.substr(0,K.I)),n}var U=function(){function e(t){var n,i,r;u(this,e),this.benchmarkLevel=0,this.network="",this.runtimeType=M.vm.UnKnow,this.sdkVersion="0.0.0",this.isVisible=y.Front,this.scene=0,this.query="",this.pluginAppId="",this.pluginVersion="",this.inited=!1;var o=t.systemInfo,s=t.launchInfo,a=t.onShow,c=t.onHide,l=t.pluginAppId,h=void 0===l?"":l,d=t.pluginVersion,g=void 0===d?"":d;a&&c?(this.inited=!0,this.onShow=a,this.onHide=c,this.benchmarkLevel=(o.benchmarkLevel||1)+100,this.sdkVersion=o.SDKVersion||"0.0.0",this.launchInfo=s,this.scene=(null===(n=this.launchInfo)||void 0===n?void 0:n.scene)||0,(0,x.xb)(null===(i=this.launchInfo)||void 0===i?void 0:i.query)||(this.query=encodeURIComponent(JSON.stringify(null===(r=this.launchInfo)||void 0===r?void 0:r.query))),this.runtimeType=(0,V.$)(o),this.pluginAppId=h,this.pluginVersion=g,this.bindNativeEvent(),this.reporter=function(e){var t,n=e.type,i=void 0===n?S.KeyValueReport:n,r=e.reportKeyValue,o=e.debug,s=e.gameTransfer,a=e.systemInfo,u=e.accountInfo,c=e.getNetworkType,l=e.onNetworkStatusChange,h=e.request,d=e.namespace,g="";function f(){return new Promise((function(e){g?e({NetworkType:g}):c({success:function(t){g=t.networkType,e({NetworkType:g})},fail:function(){e({NetworkType:g="unknown"})}})}))}return i===S.KeyValueReport?t=new L.Z({reportFunc:r,logid:26340,schemas:G,protocolParams:W,debug:o,namespace:d,asyncGetBase:f}):i===S.GameTransferReport?(t=new O({reportFunc:s,logid:26340,schemas:G,reportType:A.C2.KvStat,debug:o,namespace:d,asyncGetBase:f})).setKvStatBase(a,u):(t=new B({reportFunc:h,logid:26340,schemas:G,debug:o,namespace:d,asyncGetBase:f,reportType:A.C2.KvStat})).setKvStatBase(a,u),i!==S.GameTransferReport&&i!==S.WxRequestReport||(0,A.Mi)((function(e){g=e,t.setBase({NetworkType:e})}),c,l),t}(t),this.reporter.setBase({BenchmarkLevel:this.benchmarkLevel,RuntimeType:this.runtimeType,Scene:+this.scene,SDKVersion:this.sdkVersion,IsVisible:this.isVisible,PluginAppId:h,PluginVersion:g,FELibVersion:q,Query:this.query})):this.inited=!1}return c(e,[{key:"bindNativeEvent",value:function(){var e=this;this.onHide((function(){e.isVisible=y.Background,e.reporter.setBase({IsVisible:e.isVisible})})),this.onShow((function(t){(0,x.xb)(null==t?void 0:t.query)||(e.query=encodeURIComponent(JSON.stringify(t.query))),e.isVisible=y.Front,e.reporter.setBase({IsVisible:e.isVisible,Query:e.query})}))}},{key:"innerReport",value:function(e){this.reporter.send(Object.assign(e,{CostTime:(0,x.hj)(e.CostTime)?e.CostTime:0,Params:j(e.Params),Result:j(e.Result),ExternInfo:j(e.ExternInfo)}))}},{key:"report",value:function(e){P.indexOf(e.Type),this.inited&&this.innerReport(e)}},{key:"setCustomKey",value:function(e){this.inited&&this.reporter.setBase({CustomKey1:e.CustomKey1&&j(e.CustomKey1)||"",CustomKey2:e.CustomKey2&&j(e.CustomKey2)||"",CustomKey3:e.CustomKey3&&j(e.CustomKey3)||""})}},{key:"clearCustomKey",value:function(){this.inited&&this.reporter.setBase({CustomKey1:"",CustomKey2:"",CustomKey3:""})}}]),e}(),Q=function(){function e(t){var n=t.debug,i=void 0!==n&&n,r=t.quality,o=t.namespace;u(this,e),this.namespace="",this.errCodeIgnoreList=[],this.debug=i,this.quality=r,this.namespace=o?" ".concat(o," "):" "}return c(e,[{key:"innerRequest",value:function(e,t){var n=this;return new Promise((function(i,r){var o=t.Constructor,s=t.funcName,a=t.reportType;if(e.url){var u=new o(e);u.optionsFilter(e);var c=e.url||"";c=e.reportTargetWithoutParams?c.split("?")[0]:c,u.on("success",(function(){var t;n.debug&&e.silent;var r=u.response,o=u.sourceResponse,l=u.costTime,h=r.errcode,d=r.errmsg;e.reduceErrmsgBeforeReport&&(0,x.mf)(e.reduceErrmsgBeforeReport)&&(d=e.reduceErrmsgBeforeReport(r)),s!==k.login||r.code||(d=r.errMsg,h=9999),(0,x.hj)(h)&&0!==Number(h)&&!0!==e.silent&&-1===n.errCodeIgnoreList.indexOf(Number(h))&&(null===(t=n.quality)||void 0===t||t.innerReport({Type:a,Target:c,Params:e.data,CostTime:l,Result:{errcode:h,errmsg:d},IsError:1})),i([r,o])})),u.on("error",(function(t){var i;!0!==e.silent&&(n.debug,null===(i=n.quality)||void 0===i||i.innerReport({Type:a,Target:c,CostTime:u.costTime,Params:e.data,Result:(0,x.bV)(t),IsError:1})),r(t)})),u.on("complete",(function(){var t,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;(!0!==e.silent&&Math.random()<.1||e.forceReport)&&(null===(t=n.quality)||void 0===t||t.innerReport({Type:I.CgiSpeedMeasure,Target:c,Params:e.data,CostTime:u.costTime,IsError:i}))})),n.debug&&e.silent,u.send()}else r(w.URL_IS_EMPTY)}))}},{key:"addIgnoreErrCode",value:function(e){var t=this;(Array.isArray(e)?e:[e]).forEach((function(e){-1===t.errCodeIgnoreList.indexOf(e)&&t.errCodeIgnoreList.push(e)}))}},{key:"removeIgnoreErrCode",value:function(e){var t=this;(Array.isArray(e)?e:[e]).forEach((function(e){t.errCodeIgnoreList.indexOf(e)>-1&&t.errCodeIgnoreList.splice(t.errCodeIgnoreList.indexOf(e),1)}))}},{key:"setRequestQualityReporter",value:function(e){this.quality=e}}]),e}(),H=function(){function e(t){u(this,e),this.sessionId="",this.login=t.login,this.request=t.request}return c(e,[{key:"getSessionId",value:function(e){var t=this;return this.checkSession().catch((function(){return t.loginWxGame(e)}))}},{key:"clearSessionId",value:function(){var e,t;this.sessionId="",null===(t=(e=l.Z.wx).removeStorageSync)||void 0===t||t.call(e,_)}},{key:"checkSession",value:function(){var e=this;return new Promise((function(t,n){var i,r,o,s,a=e.sessionId||(null===(r=(i=l.Z.wx).getStorageSync)||void 0===r?void 0:r.call(i,_));a?null===(s=(o=l.Z.wx).checkSession)||void 0===s||s.call(o,{success:function(){t({sessionId:a})},fail:function(t){e.clearSessionId(),n(t)}}):n({errMsg:"尚未保存sessionId"})}))}},{key:"loginWxGame",value:function(e){var t=this;return this.isLoginPromise||(this.isLoginPromise=new Promise((function(n,i){t.login({url:"wx.login"}).then((function(n){var i={code:a(n,1)[0].code};return e&&(i.appid=e),t.request({url:"".concat(F,"/cgi-bin/gameweappauthwap/login"),data:i,method:"POST"})})).then((function(e){var r,o,s=a(e,1)[0];s&&s.data?(l.Z.isMiniGamePlugin||null===(o=(r=l.Z.wx).setStorageSync)||void 0===o||o.call(r,_,s.data.session_id),t.sessionId=s.data.session_id||"",n({sessionId:s.data.session_id})):i({errMsg:"登录出错"})})).catch((function(){i({errMsg:"登录出错"})}))}))),this.isLoginPromise.finally((function(){t.isLoginPromise=void 0}))}}]),e}();function Z(e,t){var n,i;if(!e)return"";var r=e,o=t;return o||(o=null===(i=(n=l.Z.wx).getStorageSync)||void 0===i?void 0:i.call(n,_)),o&&(r=(0,x.vr)(r,"session_id",o)),r}var X=function(e){r(n,e);var t=o(n);function n(e,i){var r;return u(this,n),(r=t.call(this)).startTime=0,r.costTime=0,r.resAsResponse=!1,r.options=e,r.requestFunc=i,r}return c(n,[{key:"optionsFilter",value:function(e){return e.sessionId&&(e.url=Z(e.url,e.sessionId)),e}},{key:"send",value:function(){var e=this;this.startTime=Date.now();var t=this.options,n=Object.assign(t,{success:function(t){e.sourceResponse=t,e.costTime=Date.now()-e.startTime;var n=(e.resAsResponse?t:t.data)||{};if((0,x.HD)(n))try{n=JSON.parse(n)}catch(t){n={}}e.response=n,e.emit("success"),e.emit("complete",0)},fail:function(t){e.costTime=Date.now()-e.startTime,e.emit("error",t),e.emit("complete",1)}});this.requestFunc(n)}}]),n}(E),J=function(e){r(n,e);var t=o(n);function n(e){return u(this,n),t.call(this,e,l.Z.isMiniGamePlugin?l.Z.pluginWx.request:l.Z.wx.request)}return c(n,[{key:"optionsFilter",value:function(e){return e.timeout=e.timeout||3e4,e.header&&(e.header=Object.assign({"content-type":"application/json; charset=utf-8"},e.header||{})),e.method=e.method?e.method.toUpperCase():"GET",e.sessionId&&(e.url=Z(e.url,e.sessionId)),e}}]),n}(X),Y=function(e){r(n,e);var t=o(n);function n(e){return u(this,n),t.call(this,e,l.Z.isMiniGamePlugin?l.Z.pluginWx.uploadFile:l.Z.wx.uploadFile)}return c(n)}(X),z=function(e){r(n,e);var t=o(n);function n(){var e;return u(this,n),(e=t.call(this,{},l.Z.isMiniGamePlugin?l.Z.pluginWx.login:l.Z.wx.login)).resAsResponse=!0,e}return c(n)}(X),$=function(e){r(n,e);var t=o(n);function n(e){var i;return u(this,n),(i=t.call(this,e,l.Z.isMiniGamePlugin?l.Z.pluginWx.downloadFile:l.Z.wx.downloadFile)).resAsResponse=!0,i}return c(n)}(X),ee=function(e){r(n,e);var t=o(n);function n(e){var i;u(this,n),(i=t.call(this,e)).login=i.initWxMethod({funcName:k.login,Constructor:z,reportType:I.Login}),i.request=i.initWxMethod({funcName:k.request,Constructor:J,reportType:I.Request}),i.uploadFile=i.initWxMethod({funcName:k.uploadFile,Constructor:Y,reportType:I.UploadFile}),i.downloadFile=i.initWxMethod({funcName:k.downloadFile,Constructor:$,reportType:I.DownloadFile}),i.session=new H({login:i.login,request:i.request});var r=e.pluginAppId,o=void 0===r?"":r;return i.pluginAppId=o,e.forceUpdateSession&&i.session.clearSessionId(),i}return c(n,[{key:"initWxMethod",value:function(e){var t=this,n=e.funcName,i=e.Constructor,r=e.reportType,o=l.Z.isMiniGamePlugin?l.Z.pluginWx:l.Z.wx;if(void 0!==o&&o[n]){var s={Constructor:i,funcName:n,reportType:r},a=function(e){return t.innerRequest(e||{},s)};return n===k.request||n===k.uploadFile?this.initMethodNeedSession(a):a}return function(){return new Promise((function(e,t){t(w.METHOD_IS_NOT_EXIST)}))}}},{key:"initMethodNeedSession",value:function(e){var t=this;return function(n){return n.needSession?t.session.getSessionId(t.pluginAppId).then((function(t){return e(Object.assign(n,t))})).then((function(i){var r,o=a(i,2),s=o[0],u=o[1];return 401===(null==u?void 0:u.statusCode)||"-1702220400"=="".concat(r=s.errcode)||"-1702220401"=="".concat(r)||"-1702220407"=="".concat(r)||"-1702220402"=="".concat(r)||"40001"=="".concat(r)?(t.session.clearSessionId(),t.session.getSessionId(t.pluginAppId).then((function(t){return e(Object.assign(n,t))}))):[s,u]})):e(n)}}}]),n}(Q),te=function(e){r(n,e);var t=o(n);function n(e){var i;return u(this,n),(i=t.call(this)).startTime=0,i.costTime=0,i.isSupportNoStringifyReqData=!1,i.options=e,i.isSupportNoStringifyReqData=!!((0,x.yC)(l.Z.systemInfo.SDKVersion,"2.29.1")>=0),i}return c(n,[{key:"optionsFilter",value:function(e){return e.sessionId&&e.data&&!e.data.session_id&&(e.data.session_id=e.sessionId),e.timeout=e.timeout||3e4,e}},{key:"send",value:function(){var e=this;this.startTime=Date.now();var t=this.options,n=t.url,i=t.data,r=t.flat,o=void 0!==r&&r,s=t.timeout,a=i||{},u=a&&(0,x.Kn)(a);u&&(a["requestTime_".concat((0,x.hQ)(10,!0))]=Date.now());var c={req_path:n,timeout:s,success:function(t){e.costTime=Date.now()-e.startTime,e.sourceResponse=t,e.response=function(e){var t=e.rawData,n=void 0===t?"":t,i=e.respData,r=n||(void 0===i?{}:i).data||"";if(!r)return{};try{return JSON.parse(r)}catch(e){return{}}}(t),e.emit("success"),e.emit("complete",0)},fail:function(t){e.costTime=Date.now()-e.startTime,e.emit("error",t),e.emit("complete",1)}};if(!o){var h=u?JSON.stringify(a):a;this.isSupportNoStringifyReqData?(c.reqData={req_path:n,json_data:h},delete c.req_path):c.json_data=h}o&&u&&(this.isSupportNoStringifyReqData?(c.reqData=Object.assign({req_path:n},a),delete c.req_path):c=Object.assign(Object.assign({},a),c)),l.Z.pluginEnv.gameTransfer(c)}}]),n}(E),ne=function(e){r(n,e);var t=o(n);function n(e){var i;return u(this,n),(i=t.call(this,e)).gameTransfer=i.initGameTransfer(),i}return c(n,[{key:"initGameTransfer",value:function(){var e=this;if(l.Z.pluginEnv.gameTransfer){var t={Constructor:te,funcName:k.gameTransfer,reportType:I.GameTransfer};return this.initMethodNeedSession((function(n){return e.innerRequest(n,t)}))}return function(){return new Promise((function(e,t){t(w.GAMETRANSFER_IS_NOT_EXIST)}))}}}]),n}(ee),ie=Date.now(),re=new(function(){function e(){u(this,e),this.version=q,this.consts=N,this.env=l.Z,this.util=i,this.config=R,this.reporter={GameTransferReport:O,KeyValueReporter:L.Z}}return c(e,[{key:"init",value:function(e){var t,i=this;if(n.g.mgp=this,this.config.set(e),this.network=new ne({pluginAppId:this.config.get("pluginAppId")||l.Z.pluginAppId||"",debug:this.config.get("debug")||!1,namespace:"mgp"}),l.Z.isMiniGamePlugin){var r=l.Z.pluginEnv;if(r.gameTransfer||r.reportKeyValue){this.quality=new U({debug:this.config.get("debug")&&this.config.get("logReport"),type:this.config.get("qualityReportType"),pluginAppId:this.config.get("pluginAppId")||l.Z.pluginAppId||"",pluginVersion:this.config.get("pluginVersion")||l.Z.pluginVersion||"",launchInfo:l.Z.wx.getLaunchOptionsSync(),accountInfo:l.Z.accountInfo,systemInfo:l.Z.systemInfo,onShow:l.Z.wx.onShow,onHide:l.Z.wx.onHide,getNetworkType:l.Z.wx.getNetworkType,onNetworkStatusChange:l.Z.wx.onNetworkStatusChange,gameTransfer:null===l.Z||void 0===l.Z?void 0:l.Z.pluginEnv.gameTransfer,reportKeyValue:null===(t=null===l.Z||void 0===l.Z?void 0:l.Z.pluginEnv)||void 0===t?void 0:t.reportKeyValue,request:function(e){i.network.request({url:e.url,method:e.method,header:e.header,data:e.data,needSession:!0,silent:!0}).then(e.success).catch(e.fail)},namespace:"mgp"}),this.network.setRequestQualityReporter(this.quality),this.quality.innerReport({Type:I.Default,Target:"init",CostTime:Date.now()-ie}),new C(this.quality)}else this.quality={report:x.YP};var o=this.config.get("debug");r.getXLogManager?this.xlog=new b({reportFunc:r.getXLogManager(),namespace:"mgp",base:{name:this.config.get("xlogName")},debug:o}):this.xlog=console}}}]),e}())},974:function(e,t,n){n.d(t,{$:function(){return r}});var i=n(580);function r(e){var t,r,o,s,a,u=null===(t=e.platform)||void 0===t?void 0:t.toLocaleLowerCase();return u===i.t4.devtools?i.vm.Devtools:u===i.t4.ios?e.model&&e.model.indexOf("iPad")>-1?(null===(r=n.g)||void 0===r?void 0:r.isIOSHighPerformanceModePlus)?i.vm.IPadWKPlus:(null===(o=n.g)||void 0===o?void 0:o.isIOSHighPerformanceMode)?i.vm.IPadWK:i.vm.IPadJscore:(null===(s=n.g)||void 0===s?void 0:s.isIOSHighPerformanceModePlus)?i.vm.IOSWKPlus:(null===(a=n.g)||void 0===a?void 0:a.isIOSHighPerformanceMode)?i.vm.IOSWK:i.vm.IOSJscore:u===i.t4.android?i.vm.Android:u===i.t4.windows||u===i.t4.mac?i.vm.PC:i.vm.UnKnow}},279:function(e){function t(){}t.prototype={on:function(e,t,n){var i=this.e||(this.e={});return(i[e]||(i[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var i=this;function r(){i.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),i=0,r=n.length;i<r;i++)n[i].fn.apply(n[i].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),i=n[e],r=[];if(i&&t)for(var o=0,s=i.length;o<s;o++)i[o].fn!==t&&i[o].fn._!==t&&r.push(i[o]);return r.length?n[e]=r:delete n[e],this}},e.exports=t,e.exports.TinyEmitter=t}},d={};function g(e){var t=d[e];if(void 0!==t)return t.exports;var n=d[e]={exports:{}};return h[e](n,n.exports,g),n.exports}g.d=function(e,t){for(var n in t)g.o(t,n)&&!g.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},g.g=function(){if("object"==("undefined"==typeof globalThis?"undefined":s(globalThis)))return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==("undefined"==typeof window?"undefined":s(window)))return window}}(),g.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},g.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var f={};(function(){g.r(f),g.d(f,{default:function(){return Ce}});var s=g(512),a=g(431).default.env;a.accountInfo.miniProgram&&a.accountInfo.miniProgram.appId;var l,h,d,v,p,m,y,I,T,S,b,x,w=a;if(void 0!==w.pluginEnv){var k,P=w.pluginEnv,R=P.customEnv;l=R.wx,m=R.canvas,h=P.reportKeyValue||function(){},P.gameTransfer,d=P.coverview,v=P.createScreenCanvas,P.PIXI,p=P.TWEEN,d?((k=d).Component,k.getPrivateThis,k.removePrivateThis,k.create,k.init,k.webviewLayout,k.xmlParser):c((function e(){u(this,e)}))}else l=GameGlobal.wx,h=function(){},function(){},function(){},function(e){return e},v=function(){};!function(e){e[e.MainCanvas=0]="MainCanvas",e[e.CoverView=1]="CoverView",e[e.ScreenCanvas=2]="ScreenCanvas"}(y||(y={})),function(e){e[e.Default=0]="Default",e[e.Webgl=1]="Webgl"}(I||(I={})),function(e){e[e.Image=0]="Image",e[e.Video=1]="Video"}(T||(T={})),function(e){e[e.Time=0]="Time",e[e.HeartBeat=1]="HeartBeat",e[e.Error=2]="Error"}(S||(S={})),function(e){e[e.Start=0]="Start",e[e.LoadImages=1]="LoadImages",e[e.Created=2]="Created",e[e.Destroy=3]="Destroy"}(b||(b={})),x||(x={});var C=function(){var e=new Map;return{time:function(t){if(!e.has(t)){var n=Date.now();return e.set(t,n),n}return 0},timeEnd:function(t){if(e.has(t)){var n=Date.now(),i=e.get(t);return e.delete(t),n-i}return 0}}}(),D=g(431).default,E=D.env.systemInfo,N=E.pixelRatio,A=(E.platform,E.benchmarkLevel,E.screenHeight),M=E.screenWidth,O=E.enableDebug,L=E.version,F=E.SDKVersion,_="1.0.25",B=O,W=D.env.isPC&&(0,s.yC)(F,"2.19.1")>=0&&!!v,G=(0,s.yC)(L,"8.0.6")>=0&&!!v||W||D.env.isDevtools,V=((0,s.yC)(F,"2.16.1"),B||D.env.isDevtools),q=function(){function e(){u(this,e),this.text="",this.bold=!1,this.textAlign="center",this.color="#000",this.fontFamily="arial",this.textBaseline="alphabetic",this.fontSize=16,this.width=M,this.x=void 0,this.y=void 0,this.right=void 0,this.bottom=20,this.groups=[],this.needScale=!0,this.textStrokeWidth=void 0,this.textStrokeColor=void 0}return c(e,[{key:"init",value:function(e){var t=e.text,n=e.textStyle,i=e.groups,r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.text=t,this.setTextStyle(n),this.groups=null!=i?i:[],this.needScale=r}},{key:"renderText",value:function(e){var t=this.fontSize,n=this.fontFamily,r=this.bold,o=this.textAlign,s=this.text,a=this.color,u=this.x,c=this.y,l=void 0===c?0:c,h=this.bottom,d=this.right,g=this.textBaseline,f=this.width;if(this.groups.length)this.renderGroups(e);else if(s){var v=[r?"bold":"","".concat(t,"px"),n].join(" ");e.font=v,e.fillStyle=a,e.textAlign=o,e.textBaseline=g;for(var p=arguments.length,m=new Array(p>1?p-1:0),y=1;y<p;y++)m[y-1]=arguments[y];var I=m.filter((function(e){return!!e})),T=I.length?[s].concat(i(I)).join(""):s,S=void 0===u?M/2:u,b=l;h&&(b=A-h),d&&(S=M-d),this.textStrokeWidth&&this.textStrokeColor&&(e.strokeStyle=this.textStrokeColor,e.lineWidth=this.textStrokeWidth,e.strokeText(T,S,b,f)),e.fillText(T,S,b,f)}}},{key:"renderGroups",value:function(e){var t=this;e.save(),!this.needScale&&e.scale(1/N,1/N),this.groups.forEach((function(n){var i=n.text,r=n.color,o=n.needPixelRadio,s=void 0!==o&&o,a=n.fontSize,u=void 0===a?16:a,c=n.fontFamily,l=void 0===c?t.fontFamily:c,h=n.left,d=n.bottom,g=n.textAlign,f=void 0===g?"center":g,v=n.textBaseline,p=void 0===v?"alphabetic":v,m=n.bold,y=void 0!==m&&m,I=n.groupText,T=void 0===I?[]:I,S=n.textStrokeWidth,b=void 0===S?0:S,x=n.textStrokeColor,w=void 0===x?"":x,k=s?N:1,P=[y?"bold":"",u*k+"px",l].join(" ");e.font=P,e.fillStyle=r,e.textAlign=f,e.textBaseline=p;var R=0,C=0,D=h*k,E=(A-d)*k;if(T.length){var O=e.measureText(i).width;D=(M*k-O)/2,T.forEach((function(t,r){var o=t.length,s=t.color;e.fillStyle=s||n.color;var a=i.slice(R,R+o);R+=o,D+=C,r!==T.length-1&&(C=e.measureText(a).width),b&&w&&(e.strokeStyle=w,e.lineWidth=b,e.strokeText(a,D,E)),e.fillText(a,D,E)}))}else b&&w&&(e.strokeStyle=w,e.lineWidth=b,e.strokeText(i,D,E,t.width)),e.fillText(i,D,E,t.width)})),e.restore()}},{key:"setTextStyle",value:function(e){if(!(0,s.xb)(e)){var t=e.fontSize,n=void 0===t?this.fontSize:t,i=e.fontFamily,r=void 0===i?this.fontFamily:i,o=e.bold,a=void 0===o?this.bold:o,u=e.textAlign,c=void 0===u?this.textAlign:u,l=e.color,h=void 0===l?this.color:l,d=e.x,g=void 0===d?this.x:d,f=e.y,v=void 0===f?this.x:f,p=e.right,m=void 0===p?this.right:p,y=e.bottom,I=void 0===y?this.bottom:y,T=e.textBaseline,S=void 0===T?this.textBaseline:T,b=e.groups,x=void 0===b?this.groups:b,w=e.textStrokeWidth,k=void 0===w?this.textStrokeWidth:w,P=e.textStrokeColor,R=void 0===P?this.textStrokeColor:P;this.fontSize=n,this.fontFamily=r,this.bold=a,this.textAlign=c,this.color=h,this.x=g,this.y=v,this.right=m,this.bottom=I,this.textBaseline=S,this.groups=x,this.textStrokeWidth=k,this.textStrokeColor=R}}}]),e}(),K=function(e){r(n,e);var t=o(n);function n(){return u(this,n),t.apply(this,arguments)}return c(n,null,[{key:"getInstance",value:function(){return n.instance||(n.instance=new n),n.instance}}]),n}(q);K.instance=void 0;var j=[],U=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"pool";u(this,e),this.name="pool",this.pool={};var n=j.find((function(e){return e.name===t}));if(n)return n;this.name=t,this.pool={},j.push(this)}return c(e,[{key:"get",value:function(e){return this.pool[e]}},{key:"set",value:function(e,t){this.pool[e]=t}},{key:"clear",value:function(){this.pool={}}},{key:"getList",value:function(){return Object.values(this.pool)}}]),e}();function Q(){}var H=g(54),Z=g(388),X=g(662).Z.wx;function J(e){(0,Z.Mi)(e,X.getNetworkType,X.onNetworkStatusChange)}var Y=function(){function e(){var t=this;u(this,e),this.startTime=0,this.visibility=!0,this.onShowCallback=function(){},this.onHideCallback=function(){},this.onHideHandler=function(){try{var e;null===(e=oe.stop)||void 0===e||e.call(oe)}catch(e){re.send({Result:e})}t.visibility=!1,t.onHideCallback()},this.onShowHandler=function(){t.visibility=!0,t.onShowCallback()},l.onShow(this.onShowHandler),l.onHide(this.onHideHandler)}return c(e,[{key:"onShow",value:function(e){e&&(this.onShowCallback=e)}},{key:"onHide",value:function(e){e&&(this.onHideCallback=e)}},{key:"destroy",value:function(){try{var e;null===(e=oe.stop)||void 0===e||e.call(oe)}catch(e){re.send({Result:e})}l.offShow(this.onShowHandler),l.offHide(this.onHideHandler)}},{key:"isVisible",get:function(){return!0===this.visibility}}],[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}}]),e}();Y.instance=void 0;var z,$=Y.getInstance(),ee=g(431).default,te=l.getLaunchOptionsSync().scene,ne=new H.Z({reportFunc:h,protocolParams:["LibVersion","PluginVersion","NetworkType","Scene","BenchmarkLevel","IsVisible","RenderTarget","ContextType","TemplateType","CostTimeMs","RunTimeMs","Type","Stage","Action","Result","ExternInfo"],schemas:{LibVersion:Z.QD.STRING,PluginVersion:Z.QD.STRING,NetworkType:Z.QD.STRING,Scene:Z.QD.UINT,BenchmarkLevel:Z.QD.INT,IsVisible:Z.QD.UINT,RenderTarget:Z.QD.UINT,ContextType:Z.QD.UINT,TemplateType:Z.QD.UINT,CostTimeMs:Z.QD.UINT,RunTimeMs:Z.QD.UINT,Type:Z.QD.UINT,Stage:Z.QD.UINT,Action:Z.QD.UINT,Result:Z.QD.STRING,ExternInfo:Z.QD.STRING},logid:23906,asyncGetBase:function(){var e=$.isVisible?1:0,t=ee.env.systemInfo,i={PluginVersion:_,IsVisible:e,LibVersion:t.SDKVersion,Scene:te,BenchmarkLevel:t.benchmarkLevel};return new Promise((function(e){J&&l.getNetworkType?J((function(t){e(n(n({},i),{},{NetworkType:t}))})):e(n(n({},i),{},{NetworkType:"4g"}))}))},debug:V}),ie=function(e){return ne.send(n(n({},e),{},{Type:S.Time,RunTimeMs:Date.now()-$.startTime}))},re={send:function(e){var t="";return e.Result&&(t=e.Result.stack?e.Result.stack.replace(/\n/gi,""):e.Result),ne.send(n(n({},e),{},{Type:S.Error,RunTimeMs:Date.now()-$.startTime,Result:t}))}},oe=function(){var e=!1,t=null;function n(){e&&(ne.send({Type:S.HeartBeat,RunTimeMs:Date.now()-$.startTime}),t=setTimeout(n,1e3))}return{start:function(){e||(e=!0,n())},stop:function(){e&&(e=!1,t&&(clearTimeout(t),t=null))}}}(),se=ne,ae=new(function(){function e(){u(this,e),this.imgPool=new U("imgPool")}return c(e,[{key:"getRes",value:function(e){return this.imgPool.get(e)}},{key:"loadImagePromise",value:function(e,t){var n=this;return new Promise((function(i,r){n.loadImage(e,t,i,r)}))}},{key:"loadImage",value:function(e,t){var n,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Q,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Q;if(!e)return null;var o=this.getRes(e),s=Date.now();if(o&&o.loadDone)i(n=o.img,!0);else if(o&&!o.loadDone)n=o.img,o.onloadcbks.push(i),o.onerrorcbks.push(r);else{var a={img:n=l.createImage(),loadDone:!1,onloadcbks:[i],onerrorcbks:[r]};this.imgPool.set(e,a),n.onload=function(){n.bindingScreen=t,ie({Stage:b.LoadImages,CostTimeMs:Date.now()-s,ExternInfo:JSON.stringify({src:e,width:n.width,height:n.height})}),a.loadDone=!0,a.onloadcbks.forEach((function(e){return e(n,!1)})),a.onloadcbks=[],a.onerrorcbks=[]},n.onerror=function(t){a.onerrorcbks.forEach((function(e){return e(t,!1)})),a.onerrorcbks=[],a.onloadcbks=[],re.send({CostTimeMs:Date.now()-s,Result:JSON.stringify({type:"loadImageError",src:e,err:t})})},n.src=e}return n}}]),e}());!function(e){e.Text="text",e.ProgressBar="progressBar"}(z||(z={}));var ue=function(){function e(t){u(this,e),this.duration=2e3,this.startTime=0,this.pauseStart=0,this.endTime=0,this.autoStart=!0,this.isPlaying=!1,this.endValue=100,this.currentProgress=0,this.isInteger=!0,this.unit="%";var n=t.duration,i=void 0===n?this.duration:n,r=t.isInteger,o=void 0===r?this.isInteger:r,s=t.unit,a=void 0===s?this.unit:s,c=t.autoStart,l=void 0===c?this.autoStart:c;this.duration=i,this.isInteger=o,this.unit=a,this.autoStart=l,l&&(this.isPlaying=!0,this.pauseStart=Date.now(),this.startTime=Date.now(),this.endTime=this.startTime+i)}return c(e,[{key:"start",value:function(){if(!this.isPlaying){this.isPlaying=!0;var e=Date.now(),t=this.startTime||e,n=e-(this.pauseStart||e);this.startTime=t+n-this.currentProgress/100*this.duration}}},{key:"stop",value:function(){this.isPlaying&&(this.isPlaying=!1,this.pauseStart=Date.now())}},{key:"setProgress",value:function(e){if(e&&e<=100){var t=this.currentProgress;this.currentProgress=e,this.isPlaying&&(this.startTime=this.startTime-(e-t)/100*this.duration)}}},{key:"setDuration",value:function(e){var t=e-this.duration;this.duration=e,this.isPlaying&&(this.startTime=this.startTime-this.currentProgress/100*t),this.endTime=this.startTime+e}},{key:"setStartTime",value:function(e){this.startTime=e}},{key:"getCurrentProgress",value:function(){if(this.isPlaying){var e=(Date.now()-this.startTime)/this.duration;e=0===this.duration||e>1?1:e,this.currentProgress=100*e}return this.currentProgress}},{key:"getCurrentProgressStr",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.unit,t=this.getCurrentProgress();return t=this.isInteger?parseInt(String(this.currentProgress),10):this.currentProgress.toFixed(1),"".concat(t).concat(e)}}]),e}(),ce=function(e){r(n,e);var t=o(n);function n(e){var i;return u(this,n),(i=t.call(this,e)).appendToLoadingText=!0,i.textInstance=void 0,i.textInstance=new q,i.textInstance.text="0".concat(i.unit),i.init(e),i}return c(n,[{key:"init",value:function(e){var t=e.appendToLoadingText,n=void 0===t||t,i=e.textStyle,r=void 0===i?{}:i;this.appendToLoadingText=n,this.textInstance.setTextStyle(r)}},{key:"update",value:function(e){var t=this.getCurrentProgressStr();this.textInstance.text=t,this.textInstance.renderText(e)}}]),n}(ue),le=function(e){r(n,e);var t=o(n);function n(e){var i,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return u(this,n),(i=t.call(this,e)).config=void 0,i.outerImage=void 0,i.innerImage=void 0,i.bindingScreen=0,i.config=e,i.bindingScreen=r,i.init(),i}return c(n,[{key:"init",value:function(){var e=this,t=this.config,n=t.outerConfig,i=t.innerConfig;null!=n&&n.src&&ae.loadImagePromise(n.src,this.bindingScreen).then((function(t){e.outerImage=t})),(null==i?void 0:i.src)&&ae.loadImagePromise(i.src,this.bindingScreen).then((function(t){e.innerImage=t}))}},{key:"update",value:function(e){var t=this.config,n=t.outerConfig,i=t.innerConfig;if(e.save(),n){var r,o,s=n.x,a=n.y,u=n.bottom,c=n.right,l=n.width,h=n.height,d=n.fillStyle,g=n.radius,f=l||(null==this||null===(r=this.outerImage)||void 0===r?void 0:r.width)||0,v=h||(null==this||null===(o=this.outerImage)||void 0===o?void 0:o.height)||0;this.drawBar(e,{x:s,y:a,bottom:u,right:c,configWidth:f,configHeight:v,fillStyle:d,radius:g},this.outerImage)}if(i){var p,m,y,I=this.getCurrentProgress()/100,T=i.x,S=i.y,b=i.bottom,x=i.right,w=i.width,k=i.height,P=i.highlight,R=i.highlightRadius,C=i.fillStyle,D=i.radius,E=w||(null==this||null===(p=this.innerImage)||void 0===p?void 0:p.width)||0,N=k||(null==this||null===(m=this.innerImage)||void 0===m?void 0:m.height)||0;this.drawBar(e,{x:T,y:S,bottom:b,right:x,displayWidth:E*I,configWidth:E,configHeight:N,sourceImageWidth:((null==this||null===(y=this.innerImage)||void 0===y?void 0:y.width)||0)*I,fillStyle:C,radius:D,highlight:P,highlightRadius:R},this.innerImage)}e.restore()}},{key:"drawBar",value:function(e,t,n){var i=t.x,r=t.y,o=void 0===r?0:r,s=t.bottom,a=t.right,u=t.configWidth,c=void 0===u?(null==n?void 0:n.width)||M:u,l=t.configHeight,h=void 0===l?(null==n?void 0:n.height)||A:l,d=t.displayWidth,g=void 0===d?c:d,f=t.displayHeight,v=void 0===f?h:f,p=t.sourceImageWidth,m=t.sourceImageHeight,y=t.fillStyle,I=t.radius,T=t.highlight,S=void 0!==T&&T,b=t.highlightRadius,x=void 0===b?0:b,w=void 0===i?(M-c)/2:i,k=o;if(s&&(k=A-s),a&&(w=M-a-c),n?e.drawImage(n,0,0,p||n.width,m||n.height,w,k,g,v):this.roundedRect(e,w,k,g,v,y,I),S&&x){var P=w+g;e.beginPath(),e.shadowColor="#FFFFFF",e.shadowBlur=10,e.arc(P,k+h/2,x,0,2*Math.PI,!1),e.fillStyle="#FFFFFF",e.fill()}}},{key:"roundedRect",value:function(e,t,n,i,r,o){var s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:0;e.beginPath(),e.moveTo(t,n+s),e.arcTo(t,n+r,t+s,n+r,s),e.arcTo(t+i,n+r,t+i,n+r-s,s),e.arcTo(t+i,n,t+i-s,n,s),e.arcTo(t,n,t,n+s,s),o&&(e.fillStyle=o),e.fill()}}]),n}(ue),he=function(){function e(){u(this,e),this.progressType=z.Text,this.progressInstance=null}return c(e,[{key:"init",value:function(e){if(!(0,s.xb)(e)){var t=e.progressType,n=void 0===t?z.Text:t,i=e.config,r=e.bindingScreen,o=void 0===r?0:r;this.progressType=n,(0,s.xb)(i)||(n===z.Text?this.progressInstance=new ce(i):n===z.ProgressBar&&(this.progressInstance=new le(i,o)))}}},{key:"update",value:function(e){this.progressInstance.update(e)}},{key:"start",value:function(){this.progressInstance.start()}},{key:"stop",value:function(){this.progressInstance.stop()}},{key:"setProgress",value:function(e){this.progressInstance.setProgress(e)}},{key:"getProgressStr",value:function(){return this.progressInstance.getCurrentProgressStr()}},{key:"getProgress",value:function(){return this.progressInstance.getCurrentProgress()}},{key:"setDuration",value:function(e){this.progressInstance.setDuration(e)}},{key:"setStartTime",value:function(e){this.progressInstance.setStartTime(e)}}],[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}}]),e}();he.instance=void 0;var de,ge=he.getInstance();function fe(e,t,n,i,r,o){var s=(r-n)/2,a=(o-i)/2,u=e/r,c=t/o,l=1;u>1&&c>1&&Math.min(u,c)>=2&&(l=Math.min(u,c)),u<1&&c<1&&Math.max(u,c)<=.5&&(l=Math.max(u,c));var h=e/n,d=t/i,g=1;return h>1&&d>1&&Math.min(h,d)>=2&&(g=Math.min(h,d)),h<1&&d<1&&Math.max(h,d)<=.5&&(g=Math.max(h,d)),{sx:(e-r*l)/2+s,sy:(t-o*l)/2+a,sw:n*g,sh:i*g}}!function(e){e.NO_BORDER="NO_BORDER",e.EXACT_FIT="EXACT_FIT",e.FIXED_HEIGHT="FIXED_HEIGHT",e.FIXED_WIDTH="FIXED_WIDTH",e.SHOW_ALL="SHOW_ALL",e.FIXED_NARROW="FIXED_NARROW",e.FIXED_WIDE="FIXED_WIDE"}(de||(de={}));var ve=function(){function e(){var t=this;u(this,e),this.canvas=null,this.ctx=null,this.inited=!1,this.destroyed=!1,this.isScreenCanvas=!1,this.scaleMode=de.NO_BORDER,this.designWidth=M,this.designHeight=A,this.imageRect=null,this.displayRect=null,this.imageInfos=void 0,this.imagesRenderInfo=void 0,this.timerId=void 0,this.tw=void 0,this.drawLoading=function(){var e=Math.PI,n=0,i=e/180,r=n+i,o=!0,s=e/90,a=0;return function(){if(!t.destroyed){var u=t.ctx;u.save(),u.beginPath(),u.lineWidth=2,u.arc(M/2,A/2,20,n,r,!0),r-n>=2*e-4*i?o=!1:r-n<=4*i&&(o=!0),o?(a=Math.sin((r-n)/2),r+=s*(7*a+1),n+=s):(a=Math.sin((r-n)/2),n+=s*(7*a+1),r+=s),u.strokeStyle="#ccc",u.lineCap="round",u.stroke(),u.restore()}}}()}return c(e,[{key:"setScaleMode",value:function(e){e&&(this.scaleMode=e)}},{key:"setDesignSize",value:function(e){(0,s.xb)(e)||(this.designWidth=e.designWidth||M,this.designHeight=e.designHeight||A)}},{key:"reset",value:function(){this.destroyed=!1,this.inited=!1}},{key:"init",value:function(e){var t=e.canvas,n=e.width,i=void 0===n?M:n,r=e.height,o=void 0===r?A:r,s=e.isScreenCanvas,a=void 0!==s&&s;!this.inited&&t&&(this.inited=!0,t.width=i,t.height=o,this.canvas=t,this.ctx=t.getContext("2d",{alpha:!0}),this.isScreenCanvas=a,a&&(t.style.width="".concat(M,"px"),t.style.height="".concat(A,"px"),t.style.top="0px",t.style.left="0px",t.style.zIndex="180"))}},{key:"destroy",value:function(){var e;this.inited&&(this.destroyed=!0,this.ctx.globalAlpha=1,this.ctx=null,this.isScreenCanvas&&null!==(e=this.canvas)&&void 0!==e&&e.remove(),this.canvas=null)}},{key:"tweenComplete",value:function(){var e=this;this.imagesRenderInfo.current=this.imagesRenderInfo.next,this.imagesRenderInfo.next=null;var t=this.imageInfos[this.imagesRenderInfo.current.index];t.displayConfig.autoSwitchNext&&(this.timerId=setTimeout((function(){e.tweenNextImage()}),t.displayConfig.showDuration||0))}},{key:"tweenNextImage",value:function(){var e=this;if(this.tw&&this.tw.isPlaying())return this.tw.stop().end(),this.tweenNextImage();this.timerId&&(clearTimeout(this.timerId),this.timerId=void 0);var t=this.imagesRenderInfo.current.index;if(t<this.imageInfos.length-1){if(null===this.imagesRenderInfo.next){var i=t+1;this.imagesRenderInfo.next=n({index:t+1,alpha:p?0:1,image:this.imageInfos[i].image},this.updateStage(this.imageInfos[i].image))}p?this.tw=new p.Tween([this.imagesRenderInfo.current,this.imagesRenderInfo.next]).to({0:{alpha:0},1:{alpha:1}},this.imageInfos[t].displayConfig.hideDuration||0).onComplete((function(){e.tweenComplete()})).start():this.tweenComplete()}return this.getCurrentImage()}},{key:"getCurrentImage",value:function(){var e,t;return(null===(e=this.imagesRenderInfo.next)||void 0===e?void 0:e.index)||(null===(t=this.imagesRenderInfo.current)||void 0===t?void 0:t.index)||0}},{key:"setImageInfo",value:function(e){var t=this;this.imageInfos=e,this.imagesRenderInfo={current:n({index:0,alpha:1,image:e[0].image},this.updateStage(e[0].image)),next:null},e.length>1&&e[0].displayConfig.autoSwitchNext&&(this.timerId=setTimeout((function(){t.tweenNextImage()}),e[0].displayConfig.showDuration||0))}},{key:"updateStage",value:function(e){if(e){var t=this.designWidth===M?e.width:this.designWidth,n=this.designHeight===A?e.height:this.designHeight,i=function(e,t,n,i,r){var o=t,s=n,a=i,u=r,c=t/a||0,l=n/u||0;switch(e){case de.EXACT_FIT:break;case de.FIXED_HEIGHT:a=Math.round(t/l);break;case de.FIXED_WIDTH:u=Math.round(n/c);break;case de.NO_BORDER:c>l?s=Math.round(u*c):o=Math.round(a*l);break;case de.SHOW_ALL:c>l?o=Math.round(a*l):s=Math.round(u*c);break;case de.FIXED_NARROW:c>l?a=Math.round(t/l):u=Math.round(n/c);break;case de.FIXED_WIDE:c>l?u=Math.round(n/c):a=Math.round(t/l);break;default:a=t,u=n}return{displayWidth:o,displayHeight:s,stageWidth:a,stageHeight:u}}(this.scaleMode,M,A,t,n),r=i.stageWidth,o=i.stageHeight,s=i.displayWidth,a=i.displayHeight;return{imageRect:fe(e.width,e.height,r,o,t,n),displayRect:{dx:(M-s)/2,dy:(A-a)/2,dw:s,dh:a}}}}},{key:"renderOneImage",value:function(e){var t=e.image,n=e.alpha,i=e.imageRect,r=e.displayRect;this.ctx.save(),this.ctx.globalAlpha=n;var o=i.sx,s=i.sy,a=i.sh,u=i.sw,c=r.dx,l=r.dy,h=r.dw,d=r.dh;this.ctx.drawImage(t,o,s,u,a,c,l,h,d),this.ctx.restore()}},{key:"renderImage",value:function(){var e;if(!this.destroyed&&null!==(e=this.imageInfos)&&void 0!==e&&e.length){var t=this.ctx,n=this.canvas,i=n.width,r=n.height;t.clearRect(0,0,i,r);var o=this.imagesRenderInfo,s=o.current,a=o.next;a&&this.renderOneImage(a),this.renderOneImage(s)}}},{key:"renderDefaultText",value:function(){if(!this.destroyed){var e="",t=ge.progressInstance;t&&(ge.progressType===z.Text&&t.appendToLoadingText?e=t.getCurrentProgressStr():t.update(this.ctx)),K.getInstance().renderText(this.ctx,e)}}}],[{key:"getInstance",value:function(){return e.instance||(e.instance=new e),e.instance}}]),e}();ve.instance=void 0;var pe=ve.getInstance(),me=function(){function e(){var t=this;u(this,e),this.ctx=void 0,this.pageConfig=void 0,this.pageType=null,this.contextType="webgl",this.contextAttributes={},this.loaded=!1,this.destroyed=!1,this.needProgressBar=!1,this.showLoading=!1,this.imageInfos=[],this.videoController=null,this.images=[],this.ticker=0,this.bindingScreen=0,this.startMainLoop=function(){var e;t.destroyed||(t.pageConfig.length>1&&p&&p.update(),null!=t&&null!==(e=t.mainLoop)&&void 0!==e&&e.call(t),t.ticker=requestAnimationFrame(t.startMainLoop))}}return c(e,[{key:"mainLoop",value:function(){}},{key:"stopMainLoop",value:function(){this.ticker&&(cancelAnimationFrame(this.ticker),this.ticker=null,p&&p.removeAll())}},{key:"baseCreate",value:function(e){var t=e.pageType,i=e.config,r=e.needProgressBar,o=void 0!==r&&r,s=e.showLoading,a=void 0!==s&&s,u=e.bindingScreen,c=void 0===u?0:u,l=e.loadingTextConfig,h=e.loadingProgressConfig,d=e.contextType,g=void 0===d?"webgl":d,f=e.contextAttributes,v=void 0===f?{}:f,p=e.needScale,m=void 0===p||p;if(this.destroyed)throw new Error("create after destroy");this.pageType=t,this.pageConfig=i,this.needProgressBar=o,this.showLoading=a,this.bindingScreen=c,this.contextType=g.toLowerCase(),this.contextAttributes=v,K.getInstance().init(l,m),ge.init(n(n({},h),{},{bindingScreen:c})),this.loaded=!0}},{key:"createPage",value:function(e){C.time("createPage"),this.loadImageInfo(e),ie({Stage:b.Created,CostTimeMs:C.timeEnd("createPage")})}},{key:"loadImageInfo",value:function(e){var t=this;if(!(this.pageConfig.length>1&&this.pageConfig.some((function(e){return function(e){return e.startsWith("http://")||e.startsWith("https://")}(e.src)})))){var i=this.pageConfig.map((function(e,i){var r=i;return ae.loadImagePromise(e.src,t.bindingScreen).then((function(i){return t.images[r]=i,t.imageInfos[r]={src:e.src,displayConfig:n({autoSwitchNext:!1,hideDuration:250,showDuration:1e3},e.displayConfig||{}),image:i},Promise.resolve(i)}))}));Promise.all(i).finally(e)}}},{key:"reset",value:function(){this.clear(),this.destroyed=!1}},{key:"clear",value:function(){this.pageConfig=null,this.images=[]}},{key:"baseDestroy",value:function(){var e=this;return new Promise((function(t){return e.loaded?e.destroyed?t():(ie({Stage:b.Destroy}),e.destroyed=!0,void(e.pageType===T.Video?e.videoController.destroy().then(t):(pe.destroy(),e.clear(),e.ticker&&(cancelAnimationFrame(e.ticker),e.ticker=null),setTimeout(t,300)))):(e.destroyed=!0,t())}))}},{key:"setLoadingText",value:function(e){}},{key:"setProgress",value:function(e){ge.setProgress(e)}},{key:"getProgressStr",value:function(){return ge.getProgressStr()}},{key:"getProgress",value:function(){return ge.getProgress()}},{key:"setDuration",value:function(e){ge.setDuration(e)}},{key:"setStartTime",value:function(e){ge.setStartTime(e)}},{key:"startProgress",value:function(){ge.start()}},{key:"stopProgress",value:function(){ge.stop()}}],[{key:"getInstance",value:function(){return this.instance||(this.instance=new this),this.instance}}]),e}(),ye={},Ie=[-1,-1,0,1,-1,0,1,1,0,-1,1,0],Te=[0,1,2,0,2,3],Se=[0,0,1,0,1,1,0,1];function be(e,t,n){var i=e.createShader(n);return e.shaderSource(i,t),e.compileShader(i),e.getShaderParameter(i,e.COMPILE_STATUS),i}var xe=function(n){r(s,n);var i=o(s);function s(){var e;u(this,s);for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return(e=i.call.apply(i,[this].concat(n))).renderer=void 0,e.mainLoop=function(){e.ctx.clearColor(0,0,0,0),e.ctx.clear(e.ctx.COLOR_BUFFER_BIT),pe.renderImage(),e.showLoading&&pe.drawLoading(),pe.renderDefaultText(),e.renderer(pe.canvas)},e}return c(s,[{key:"create",value:function(e){var t=this;return new Promise((function(n,i){try{t.baseCreate(e),t.pageType===T.Image&&(t.ctx=m.getContext("webgl",e.contextAttributes||{}),t.renderer=function(e){if(e){e.frontFace(e.CCW),e.canvas.width=M*N,e.canvas.height=A*N,e.viewport(0,0,e.canvas.width,e.canvas.height);var t=be(e,"\n  attribute vec3 aPos;\n  attribute vec2 aVertexTextureCoord;\n  varying highp vec2 vTextureCoord;\n\n  void main(void){\n    gl_Position = vec4(aPos, 1);\n    vTextureCoord = aVertexTextureCoord;\n  }\n",e.VERTEX_SHADER),n=be(e,"\n  varying highp vec2 vTextureCoord;\n  uniform sampler2D uSampler;\n\n  void main(void) {\n    gl_FragColor = texture2D(uSampler, vTextureCoord);\n  }\n",e.FRAGMENT_SHADER),i=e.createProgram();if(e.attachShader(i,t),e.attachShader(i,n),e.linkProgram(i),e.getProgramParameter(i,e.LINK_STATUS)){e.useProgram(i);var r=e.createTexture();e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,r),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.bindTexture(e.TEXTURE_2D,null),ye.vertexBuffer=e.createBuffer(),e.bindBuffer(e.ARRAY_BUFFER,ye.vertexBuffer),e.bufferData(e.ARRAY_BUFFER,new Float32Array(Ie),e.STATIC_DRAW),ye.vertexIndiceBuffer=e.createBuffer(),e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,ye.vertexIndiceBuffer),e.bufferData(e.ELEMENT_ARRAY_BUFFER,new Uint16Array(Te),e.STATIC_DRAW);var o=e.getAttribLocation(i,"aPos");e.vertexAttribPointer(o,3,e.FLOAT,!1,0,0),e.enableVertexAttribArray(o),ye.trianglesTexCoordBuffer=e.createBuffer(),e.bindBuffer(e.ARRAY_BUFFER,ye.trianglesTexCoordBuffer),e.bufferData(e.ARRAY_BUFFER,new Float32Array(Se),e.STATIC_DRAW);var s=e.getAttribLocation(i,"aVertexTextureCoord");e.enableVertexAttribArray(s),e.vertexAttribPointer(s,2,e.FLOAT,!1,0,0);var a=e.getUniformLocation(i,"uSampler");return e.uniform1i(a,0),function(t){e.clearColor(0,0,0,0),e.clear(e.COLOR_BUFFER_BIT),e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,!0),e.bindTexture(e.TEXTURE_2D,r),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t),e.drawElements(e.TRIANGLES,6,e.UNSIGNED_SHORT,0),e.pixelStorei(e.UNPACK_FLIP_Y_WEBGL,!1)}}}}(t.ctx),pe.init({canvas:l.createCanvas(),width:M*N,height:A*N}),pe.ctx.scale(N,N)),t.createPage((function(){pe.setImageInfo(t.imageInfos),n()}))}catch(e){i(e)}}))}},{key:"destroy",value:function(){return this.ctx=null,this.baseDestroy()}},{key:"reset",value:function(){e(t(s.prototype),"reset",this).call(this),pe.reset()}}]),s}(me),we=null,ke=function(n){r(s,n);var i=o(s);function s(){var e;u(this,s);for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return(e=i.call.apply(i,[this].concat(n))).mainLoop=function(){pe.renderImage(),e.showLoading&&pe.drawLoading(),pe.renderDefaultText()},e}return c(s,[{key:"create",value:function(e){var t=this;return we=v(),e.bindingScreen=we.bindingScreen,new Promise((function(n,i){try{t.baseCreate(e),t.pageType===T.Image&&(pe.init({canvas:we,width:M*N,height:A*N,isScreenCanvas:!0}),pe.ctx.scale(N,N)),t.createPage((function(){pe.setImageInfo(t.imageInfos),n()}))}catch(e){i(e)}}))}},{key:"destroy",value:function(){return we=null,this.baseDestroy()}},{key:"reset",value:function(){e(t(s.prototype),"reset",this).call(this),pe.reset()}}]),s}(me),Pe=function(n){r(s,n);var i=o(s);function s(){var e;u(this,s);for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return(e=i.call.apply(i,[this].concat(n))).mainLoop=function(){pe.renderImage(),e.showLoading&&pe.drawLoading(),pe.renderDefaultText()},e}return c(s,[{key:"create",value:function(e){var t=this;return new Promise((function(n,i){try{t.baseCreate(e),t.pageType===T.Image&&pe.init({canvas:m}),t.createPage((function(){pe.setImageInfo(t.imageInfos),n()}))}catch(e){i(e)}}))}},{key:"destroy",value:function(){return this.baseDestroy()}},{key:"reset",value:function(){e(t(s.prototype),"reset",this).call(this),pe.reset()}}]),s}(me),Re=g(431).default;Re.init({debug:!1,logReport:!1,pluginAppId:"wxbd990766293b9dc4",pluginVersion:_,xlogName:"minigameLoading"});var Ce=new(function(){function e(){u(this,e),this.pageManager=null,this.pageType=null,this.ScaleMode=de,this.isMainCanvas=!0}return c(e,[{key:"create",value:function(e){var t=this,n=e.images,i=e.video,r=e.needProgressBar,o=e.contextType,a=e.contextAttributes,u=void 0===a?{}:a,c=e.showLoading,l=e.scaleMode,h=e.designWidth,d=e.designHeight,g=e.loadingTextConfig,f=void 0===g?{}:g,v=e.loadingProgressConfig,p=void 0===v?{}:v,S=e.useMainCanvas,x=void 0!==S&&S,w=e.needScale,k=void 0===w||w;$.startTime=Date.now();var P="webgl"===o.toLowerCase()?I.Webgl:I.Default;P===I.Webgl&&(0,s.xb)(u)&&Re.env.isDevtools;var R=!(0,s.xb)(i);this.pageType=R?T.Video:T.Image;var C=G&&!x?y.ScreenCanvas:y.MainCanvas;return this.isMainCanvas=C===y.MainCanvas,se.setBase({RenderTarget:C,ContextType:P,TemplateType:this.pageType}),ie({Stage:b.Start,CostTimeMs:Date.now()-$.startTime,ExternInfo:JSON.stringify(e)}),(0,s.xb)(l)||this.setScaleMode(l),h&&d&&this.setDesignSize({designWidth:h,designHeight:d}),C===y.ScreenCanvas?(m.getContext(o.toLowerCase(),u||{}),this.pageManager=ke.getInstance()):o&&P===I.Webgl?this.pageManager=xe.getInstance():this.pageManager=Pe.getInstance(),this.pageManager.create({pageType:this.pageType,config:R?i:n,needProgressBar:r,showLoading:c,contextType:o,contextAttributes:u,loadingTextConfig:f,loadingProgressConfig:p,needScale:k}).then((function(){return t.pageManager.startMainLoop(),Promise.resolve()})).catch((function(e){return re.send({Result:e}),$.destroy(),Promise.reject(e)}))}},{key:"destroy",value:function(){return this.pageManager.destroy().then((function(){return $.destroy(),Promise.resolve()}))}},{key:"reset",value:function(){var e;return null===(e=this.pageManager)||void 0===e?void 0:e.reset()}},{key:"startProgress",value:function(){this.pageManager.startProgress()}},{key:"stopProgress",value:function(){this.pageManager.stopProgress()}},{key:"setDuration",value:function(e){return this.pageManager.setDuration(e)}},{key:"setProgress",value:function(e){return this.pageManager.setProgress(e)}},{key:"setLoadingText",value:function(e){K.getInstance().text=e}},{key:"setLoadingTextStyle",value:function(e){K.getInstance().setTextStyle(e)}},{key:"setLoadingGroups",value:function(e){K.getInstance().groups=e}},{key:"setScaleMode",value:function(e){pe.setScaleMode(e)}},{key:"setDesignSize",value:function(e){pe.setDesignSize(e)}},{key:"getProgressStr",value:function(){return this.pageManager.getProgressStr()}},{key:"setStartTime",value:function(e){return this.pageManager.setStartTime(e)}},{key:"getProgress",value:function(){return this.pageManager.getProgress()}},{key:"showNextImage",value:function(){return pe.tweenNextImage()}},{key:"getCurrentImage",value:function(){return pe.getCurrentImage()}}]),e}())})(),module.exports=f}(); 
 			}); 
	