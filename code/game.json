{"deviceOrientation": "portrait", "networkTimeout": {"request": 15000, "connectSocket": 10000, "uploadFile": 10000, "downloadFile": 8000}, "iOSHighPerformance": true, "permission": {"scope.userFuzzyLocation": {"desc": "授权位置信息，参与地区竞赛！"}}, "navigateToMiniProgramAppIdList": [], "subPackages": [{"name": "main_scripts", "root": "/subpackages/main_scripts/"}, {"name": "ab_scripts", "root": "/subpackages/ab_scripts/"}, {"name": "cocos", "root": "/cocos/"}, {"name": "qzz_sdk", "root": "/sdk/"}, {"name": "adv_test", "root": "/subpackages/adv_test/"}, {"name": "bind_phone", "root": "/subpackages/bind_phone/"}, {"name": "buff", "root": "/subpackages/buff/"}, {"name": "buff_goldenegg_boom", "root": "/subpackages/buff_goldenegg_boom/"}, {"name": "choice_pack", "root": "/subpackages/choice_pack/"}, {"name": "double_energy_info", "root": "/subpackages/double_energy_info/"}, {"name": "energy_lack", "root": "/subpackages/energy_lack/"}, {"name": "game_circle", "root": "/subpackages/game_circle/"}, {"name": "gm_new", "root": "/subpackages/gm_new/"}, {"name": "gm_old", "root": "/subpackages/gm_old/"}, {"name": "level_view", "root": "/subpackages/level_view/"}, {"name": "mail", "root": "/subpackages/mail/"}, {"name": "map_change", "root": "/subpackages/map_change/"}, {"name": "network_error", "root": "/subpackages/network_error/"}, {"name": "open_box", "root": "/subpackages/open_box/"}, {"name": "setting", "root": "/subpackages/setting/"}, {"name": "story", "root": "/subpackages/story/"}, {"name": "subscribe", "root": "/subpackages/subscribe/"}, {"name": "task_build_info", "root": "/subpackages/task_build_info/"}, {"name": "temp_desktop", "root": "/subpackages/temp_desktop/"}, {"name": "activity1_main", "root": "/subpackages/activity1_main/"}, {"name": "activity1_notice", "root": "/subpackages/activity1_notice/"}, {"name": "activity1_open", "root": "/subpackages/activity1_open/"}, {"name": "activity_over", "root": "/subpackages/activity_over/"}, {"name": "activity_rank", "root": "/subpackages/activity_rank/"}, {"name": "activity_rank_info", "root": "/subpackages/activity_rank_info/"}, {"name": "activity_rank_reward", "root": "/subpackages/activity_rank_reward/"}, {"name": "buy_cat", "root": "/subpackages/buy_cat/"}, {"name": "buy_new", "root": "/subpackages/buy_new/"}, {"name": "buy_queencard", "root": "/subpackages/buy_queencard/"}, {"name": "cards_opencard", "root": "/subpackages/cards_opencard/"}, {"name": "champion_info", "root": "/subpackages/champion_info/"}, {"name": "champion_join", "root": "/subpackages/champion_join/"}, {"name": "champion_rank", "root": "/subpackages/champion_rank/"}, {"name": "champion_reward", "root": "/subpackages/champion_reward/"}, {"name": "champion_task_hide", "root": "/subpackages/champion_task_hide/"}, {"name": "champion_tip", "root": "/subpackages/champion_tip/"}, {"name": "clone_event_info1", "root": "/subpackages/clone_event_info1/"}, {"name": "clone_event_info2", "root": "/subpackages/clone_event_info2/"}, {"name": "item_selltip", "root": "/subpackages/item_selltip/"}, {"name": "dress_main", "root": "/subpackages/dress_main/"}, {"name": "dress_notice", "root": "/subpackages/dress_notice/"}, {"name": "gift_endless", "root": "/subpackages/gift_endless/"}, {"name": "gift_free", "root": "/subpackages/gift_free/"}, {"name": "gift_growth", "root": "/subpackages/gift_growth/"}, {"name": "gift_pig", "root": "/subpackages/gift_pig/"}, {"name": "gift_rebate", "root": "/subpackages/gift_rebate/"}, {"name": "michelin_info", "root": "/subpackages/michelin_info/"}, {"name": "michelin_rank", "root": "/subpackages/michelin_rank/"}, {"name": "michelin_reward", "root": "/subpackages/michelin_reward/"}, {"name": "michelin_select", "root": "/subpackages/michelin_select/"}, {"name": "month_pay_buy", "root": "/subpackages/month_pay_buy/"}, {"name": "month_pay_info", "root": "/subpackages/month_pay_info/"}, {"name": "notice_secure", "root": "/subpackages/notice_secure/"}, {"name": "passport_lvup_tip", "root": "/subpackages/passport_lvup_tip/"}, {"name": "passport_tipinfo", "root": "/subpackages/passport_tipinfo/"}, {"name": "player_badge_list", "root": "/subpackages/player_badge_list/"}, {"name": "player_badge_show", "root": "/subpackages/player_badge_show/"}, {"name": "player_head", "root": "/subpackages/player_head/"}, {"name": "player_help", "root": "/subpackages/player_help/"}, {"name": "player_info", "root": "/subpackages/player_info/"}, {"name": "rabbit_back", "root": "/subpackages/rabbit_back/"}, {"name": "rabbit_guide", "root": "/subpackages/rabbit_guide/"}, {"name": "rabbit_help", "root": "/subpackages/rabbit_help/"}, {"name": "rabbit_main", "root": "/subpackages/rabbit_main/"}, {"name": "rabbit_rank", "root": "/subpackages/rabbit_rank/"}, {"name": "rabbit_rule", "root": "/subpackages/rabbit_rule/"}, {"name": "rabbit_shop", "root": "/subpackages/rabbit_shop/"}, {"name": "block", "root": "/subpackages/block/"}, {"name": "dart", "root": "/subpackages/dart/"}, {"name": "num", "root": "/subpackages/num/"}, {"name": "ui_entry", "root": "/subpackages/ui_entry/"}, {"name": "ui_over", "root": "/subpackages/ui_over/"}, {"name": "activity_plot1", "root": "/subpackages/activity_plot1/"}, {"name": "activity_plot1_over", "root": "/subpackages/activity_plot1_over/"}, {"name": "activity_plot1_pay", "root": "/subpackages/activity_plot1_pay/"}, {"name": "activity_plot_continue", "root": "/subpackages/activity_plot_continue/"}], "__warning__": "无效的 game.json permission[\"scope.userFuzzyLocation\"]、game.json [\"iOSHighPerformance\"]、game.json [\"navigateToMiniProgramAppIdList\"]", "gamePlugins": {"MinigameLoading": {"version": "latest", "provider": "wxbd990766293b9dc4", "contexts": [{"type": "isolatedContext"}]}}}