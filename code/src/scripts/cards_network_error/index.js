var e = require("../../../@babel/runtime/helpers/typeof");

window.__require = function e(t, r, o) {
    function n(p, c) {
        if (!r[p]) {
            if (!t[p]) {
                var u = p.split("/");
                if (u = u[u.length - 1], !t[u]) {
                    var a = "function" == typeof __require && __require;
                    if (!c && a) return a(u, !0);
                    if (i) return i(u, !0);
                    throw new Error("Cannot find module '" + p + "'");
                }
                p = u;
            }
            var f = r[p] = {
                exports: {}
            };
            t[p][0].call(f.exports, function(e) {
                return n(t[p][1][e] || e);
            }, f, f.exports, e, t, r, o);
        }
        return r[p].exports;
    }
    for (var i = "function" == typeof __require && __require, p = 0; p < o.length; p++) n(o[p]);
    return n;
}({
    CardsNetworkErrorPopwin: [ function(t, r, o) {
        cc._RF.push(r, "72890GXdi1EcZ5vUgyIbaI3", "CardsNetworkErrorPopwin");
        var n, i = this && this.__extends || (n = function(e, t) {
            return (n = Object.setPrototypeOf || {
                __proto__: []
            } instanceof Array && function(e, t) {
                e.__proto__ = t;
            } || function(e, t) {
                for (var r in t) Object.prototype.hasOwnProperty.call(t, r) && (e[r] = t[r]);
            })(e, t);
        }, function(e, t) {
            function r() {
                this.constructor = e;
            }
            n(e, t), e.prototype = null === t ? Object.create(t) : (r.prototype = t.prototype, 
            new r());
        }), p = this && this.__decorate || function(t, r, o, n) {
            var i, p = arguments.length, c = p < 3 ? r : null === n ? n = Object.getOwnPropertyDescriptor(r, o) : n;
            if ("object" == ("undefined" == typeof Reflect ? "undefined" : e(Reflect)) && "function" == typeof Reflect.decorate) c = Reflect.decorate(t, r, o, n); else for (var u = t.length - 1; u >= 0; u--) (i = t[u]) && (c = (p < 3 ? i(c) : p > 3 ? i(r, o, c) : i(r, o)) || c);
            return p > 3 && c && Object.defineProperty(r, o, c), c;
        };
        Object.defineProperty(o, "__esModule", {
            value: !0
        });
        var c = t("../../../Script/Common/Action"), u = t("../../../Script/Audio/AudioMgr"), a = t("../../../Script/Manager/OnlineMgr"), f = t("../../../Script/View/base/BasePopwin"), l = t("../../../Script/Utils/WXApi"), s = cc._decorator, d = s.ccclass, _ = (s.property, 
        function(e) {
            function t() {
                var t = null !== e && e.apply(this, arguments) || this;
                return t.prefabPath = "./prefab/popwin_cards_network_error", t;
            }
            var r;
            return i(t, e), r = t, t.prototype.onOpen = function(t) {
                e.prototype.onOpen.call(this, t);
            }, t.prototype.openAnim = function() {
                return c.default.playAnim(this.node);
            }, Object.defineProperty(t.prototype, "isRelease", {
                get: function() {
                    return !1;
                },
                enumerable: !1,
                configurable: !0
            }), Object.defineProperty(t.prototype, "BannerOpen", {
                get: function() {
                    return a.OnlineMgr.getBanner(this.prefabPath);
                },
                enumerable: !1,
                configurable: !0
            }), t.prototype.onRetry = function() {
                u.AudioMgr.button(), this.handleClose(), l.default.I.restart();
            }, t.prototype.handleClose = function() {
                this.doClose(r);
            }, r = p([ d ], t);
        }(f.default));
        o.default = _, cc._RF.pop();
    }, {
        "../../../Script/Audio/AudioMgr": void 0,
        "../../../Script/Common/Action": void 0,
        "../../../Script/Manager/OnlineMgr": void 0,
        "../../../Script/Utils/WXApi": void 0,
        "../../../Script/View/base/BasePopwin": void 0
    } ]
}, {}, [ "CardsNetworkErrorPopwin" ]);