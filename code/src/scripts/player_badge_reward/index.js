var e = require("../../../@babel/runtime/helpers/typeof");

window.__require = function e(t, r, n) {
    function o(a, c) {
        if (!r[a]) {
            if (!t[a]) {
                var p = a.split("/");
                if (p = p[p.length - 1], !t[p]) {
                    var u = "function" == typeof __require && __require;
                    if (!c && u) return u(p, !0);
                    if (i) return i(p, !0);
                    throw new Error("Cannot find module '" + a + "'");
                }
                a = p;
            }
            var l = r[a] = {
                exports: {}
            };
            t[a][0].call(l.exports, function(e) {
                return o(t[a][1][e] || e);
            }, l, l.exports, e, t, r, n);
        }
        return r[a].exports;
    }
    for (var i = "function" == typeof __require && __require, a = 0; a < n.length; a++) o(n[a]);
    return o;
}({
    PlayerBadgeRewardPopwin: [ function(t, r, n) {
        cc._RF.push(r, "0c9feabhQ5N9rwDV1cfsP8u", "PlayerBadgeRewardPopwin");
        var o, i = this && this.__extends || (o = function(e, t) {
            return (o = Object.setPrototypeOf || {
                __proto__: []
            } instanceof Array && function(e, t) {
                e.__proto__ = t;
            } || function(e, t) {
                for (var r in t) Object.prototype.hasOwnProperty.call(t, r) && (e[r] = t[r]);
            })(e, t);
        }, function(e, t) {
            function r() {
                this.constructor = e;
            }
            o(e, t), e.prototype = null === t ? Object.create(t) : (r.prototype = t.prototype, 
            new r());
        }), a = this && this.__decorate || function(t, r, n, o) {
            var i, a = arguments.length, c = a < 3 ? r : null === o ? o = Object.getOwnPropertyDescriptor(r, n) : o;
            if ("object" == ("undefined" == typeof Reflect ? "undefined" : e(Reflect)) && "function" == typeof Reflect.decorate) c = Reflect.decorate(t, r, n, o); else for (var p = t.length - 1; p >= 0; p--) (i = t[p]) && (c = (a < 3 ? i(c) : a > 3 ? i(r, n, c) : i(r, n)) || c);
            return a > 3 && c && Object.defineProperty(r, n, c), c;
        }, c = this && this.__awaiter || function(e, t, r, n) {
            return new (r || (r = Promise))(function(o, i) {
                function a(e) {
                    try {
                        p(n.next(e));
                    } catch (e) {
                        i(e);
                    }
                }
                function c(e) {
                    try {
                        p(n.throw(e));
                    } catch (e) {
                        i(e);
                    }
                }
                function p(e) {
                    var t;
                    e.done ? o(e.value) : (t = e.value, t instanceof r ? t : new r(function(e) {
                        e(t);
                    })).then(a, c);
                }
                p((n = n.apply(e, t || [])).next());
            });
        }, p = this && this.__generator || function(e, t) {
            var r, n, o, i, a = {
                label: 0,
                sent: function() {
                    if (1 & o[0]) throw o[1];
                    return o[1];
                },
                trys: [],
                ops: []
            };
            return i = {
                next: c(0),
                throw: c(1),
                return: c(2)
            }, "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                return this;
            }), i;
            function c(e) {
                return function(t) {
                    return p([ e, t ]);
                };
            }
            function p(i) {
                if (r) throw new TypeError("Generator is already executing.");
                for (;a; ) try {
                    if (r = 1, n && (o = 2 & i[0] ? n.return : i[0] ? n.throw || ((o = n.return) && o.call(n), 
                    0) : n.next) && !(o = o.call(n, i[1])).done) return o;
                    switch (n = 0, o && (i = [ 2 & i[0], o.value ]), i[0]) {
                      case 0:
                      case 1:
                        o = i;
                        break;

                      case 4:
                        return a.label++, {
                            value: i[1],
                            done: !1
                        };

                      case 5:
                        a.label++, n = i[1], i = [ 0 ];
                        continue;

                      case 7:
                        i = a.ops.pop(), a.trys.pop();
                        continue;

                      default:
                        if (!(o = (o = a.trys).length > 0 && o[o.length - 1]) && (6 === i[0] || 2 === i[0])) {
                            a = 0;
                            continue;
                        }
                        if (3 === i[0] && (!o || i[1] > o[0] && i[1] < o[3])) {
                            a.label = i[1];
                            break;
                        }
                        if (6 === i[0] && a.label < o[1]) {
                            a.label = o[1], o = i;
                            break;
                        }
                        if (o && a.label < o[2]) {
                            a.label = o[2], a.ops.push(i);
                            break;
                        }
                        o[2] && a.ops.pop(), a.trys.pop();
                        continue;
                    }
                    i = t.call(e, a);
                } catch (e) {
                    i = [ 6, e ], n = 0;
                } finally {
                    r = o = 0;
                }
                if (5 & i[0]) throw i[1];
                return {
                    value: i[0] ? i[1] : void 0,
                    done: !0
                };
            }
        };
        Object.defineProperty(n, "__esModule", {
            value: !0
        });
        var u = t("../../../Script/Common/Display"), l = t("../../../Script/ResKit/ResMgr"), s = t("../../../Script/Audio/AudioMgr"), f = t("../../../Script/Manager/CommonMgr"), d = t("../../../Script/Manager/OnlineMgr"), h = t("../../../Script/Data/DataServerMgr"), y = t("../../../Script/View/base/BaseAnimPopwin"), g = t("../../../Script/Manager/SubpackageMgr"), v = cc._decorator, b = v.ccclass, _ = v.property, w = function(e) {
            function t() {
                var t = null !== e && e.apply(this, arguments) || this;
                return t.prefabPath = "./prefab/popwin_player_badge_reward", t.imgIcon = null, t.txtName = null, 
                t.txtContent = null, t.txtExtContent = null, t;
            }
            var r;
            return i(t, e), r = t, t.prototype.onOpen = function(t) {
                e.prototype.onOpen.call(this, t);
                var r = h.DataServerMgr.Achievement;
                this.cfg = r.getCfgById(t.data), this.data = r.BadgeDatas[t.data], this.initUI();
            }, t.prototype.onTouchMask = function() {
                this.onBack();
            }, t.prototype.onClosed = function() {
                e.prototype.onClosed.call(this), l.ResMgr.releaseRef(this), g.default.I.releaseBundle(g.AssetBundleName.PlayerBadgeReward);
            }, Object.defineProperty(t.prototype, "isRelease", {
                get: function() {
                    return !0;
                },
                enumerable: !1,
                configurable: !0
            }), Object.defineProperty(t.prototype, "BannerOpen", {
                get: function() {
                    return d.OnlineMgr.getBanner(this.prefabPath);
                },
                enumerable: !1,
                configurable: !0
            }), t.prototype.initUI = function() {
                var e = this.cfg, t = e.name, r = (e.content, e.ext_content, e.pic_name);
                this.txtName.string = t, this.loadSprite(r);
            }, t.prototype.loadSprite = function(e) {
                return c(this, void 0, void 0, function() {
                    var t = this;
                    return p(this, function() {
                        return [ 2, f.default.I.loadAchieveIcon(e).then(function(e) {
                            cc.isValid(t.imgIcon) && (t.imgIcon.spriteFrame = e, l.ResMgr.retainRefAsset(t, e), 
                            u.default.autoSize(t.imgIcon, cc.size(238, 240)));
                        }) ];
                    });
                });
            }, t.prototype.onBack = function() {
                s.AudioMgr.button(), this.handleClose();
            }, t.prototype.handleClose = function() {
                this.doClose(r);
            }, a([ _(cc.Sprite) ], t.prototype, "imgIcon", void 0), a([ _(cc.Label) ], t.prototype, "txtName", void 0), 
            a([ _(cc.Label) ], t.prototype, "txtContent", void 0), a([ _(cc.Label) ], t.prototype, "txtExtContent", void 0), 
            r = a([ b ], t);
        }(y.default);
        n.default = w, cc._RF.pop();
    }, {
        "../../../Script/Audio/AudioMgr": void 0,
        "../../../Script/Common/Display": void 0,
        "../../../Script/Data/DataServerMgr": void 0,
        "../../../Script/Manager/CommonMgr": void 0,
        "../../../Script/Manager/OnlineMgr": void 0,
        "../../../Script/Manager/SubpackageMgr": void 0,
        "../../../Script/ResKit/ResMgr": void 0,
        "../../../Script/View/base/BaseAnimPopwin": void 0
    } ]
}, {}, [ "PlayerBadgeRewardPopwin" ]);