var e = require("../../../@babel/runtime/helpers/typeof");

window.__require = function e(t, r, o) {
    function n(a, p) {
        if (!r[a]) {
            if (!t[a]) {
                var c = a.split("/");
                if (c = c[c.length - 1], !t[c]) {
                    var u = "function" == typeof __require && __require;
                    if (!p && u) return u(c, !0);
                    if (i) return i(c, !0);
                    throw new Error("Cannot find module '" + a + "'");
                }
                a = c;
            }
            var l = r[a] = {
                exports: {}
            };
            t[a][0].call(l.exports, function(e) {
                return n(t[a][1][e] || e);
            }, l, l.exports, e, t, r, o);
        }
        return r[a].exports;
    }
    for (var i = "function" == typeof __require && __require, a = 0; a < o.length; a++) n(o[a]);
    return n;
}({
    LevelGiftPopwin: [ function(t, r, o) {
        cc._RF.push(r, "eed8aJeSe9HaZuuDUwRpM4F", "LevelGiftPopwin");
        var n, i = this && this.__extends || (n = function(e, t) {
            return (n = Object.setPrototypeOf || {
                __proto__: []
            } instanceof Array && function(e, t) {
                e.__proto__ = t;
            } || function(e, t) {
                for (var r in t) Object.prototype.hasOwnProperty.call(t, r) && (e[r] = t[r]);
            })(e, t);
        }, function(e, t) {
            function r() {
                this.constructor = e;
            }
            n(e, t), e.prototype = null === t ? Object.create(t) : (r.prototype = t.prototype, 
            new r());
        }), a = this && this.__decorate || function(t, r, o, n) {
            var i, a = arguments.length, p = a < 3 ? r : null === n ? n = Object.getOwnPropertyDescriptor(r, o) : n;
            if ("object" == ("undefined" == typeof Reflect ? "undefined" : e(Reflect)) && "function" == typeof Reflect.decorate) p = Reflect.decorate(t, r, o, n); else for (var c = t.length - 1; c >= 0; c--) (i = t[c]) && (p = (a < 3 ? i(p) : a > 3 ? i(r, o, p) : i(r, o)) || p);
            return a > 3 && p && Object.defineProperty(r, o, p), p;
        }, p = this && this.__awaiter || function(e, t, r, o) {
            return new (r || (r = Promise))(function(n, i) {
                function a(e) {
                    try {
                        c(o.next(e));
                    } catch (e) {
                        i(e);
                    }
                }
                function p(e) {
                    try {
                        c(o.throw(e));
                    } catch (e) {
                        i(e);
                    }
                }
                function c(e) {
                    var t;
                    e.done ? n(e.value) : (t = e.value, t instanceof r ? t : new r(function(e) {
                        e(t);
                    })).then(a, p);
                }
                c((o = o.apply(e, t || [])).next());
            });
        }, c = this && this.__generator || function(e, t) {
            var r, o, n, i, a = {
                label: 0,
                sent: function() {
                    if (1 & n[0]) throw n[1];
                    return n[1];
                },
                trys: [],
                ops: []
            };
            return i = {
                next: p(0),
                throw: p(1),
                return: p(2)
            }, "function" == typeof Symbol && (i[Symbol.iterator] = function() {
                return this;
            }), i;
            function p(e) {
                return function(t) {
                    return c([ e, t ]);
                };
            }
            function c(i) {
                if (r) throw new TypeError("Generator is already executing.");
                for (;a; ) try {
                    if (r = 1, o && (n = 2 & i[0] ? o.return : i[0] ? o.throw || ((n = o.return) && n.call(o), 
                    0) : o.next) && !(n = n.call(o, i[1])).done) return n;
                    switch (o = 0, n && (i = [ 2 & i[0], n.value ]), i[0]) {
                      case 0:
                      case 1:
                        n = i;
                        break;

                      case 4:
                        return a.label++, {
                            value: i[1],
                            done: !1
                        };

                      case 5:
                        a.label++, o = i[1], i = [ 0 ];
                        continue;

                      case 7:
                        i = a.ops.pop(), a.trys.pop();
                        continue;

                      default:
                        if (!(n = (n = a.trys).length > 0 && n[n.length - 1]) && (6 === i[0] || 2 === i[0])) {
                            a = 0;
                            continue;
                        }
                        if (3 === i[0] && (!n || i[1] > n[0] && i[1] < n[3])) {
                            a.label = i[1];
                            break;
                        }
                        if (6 === i[0] && a.label < n[1]) {
                            a.label = n[1], n = i;
                            break;
                        }
                        if (n && a.label < n[2]) {
                            a.label = n[2], a.ops.push(i);
                            break;
                        }
                        n[2] && a.ops.pop(), a.trys.pop();
                        continue;
                    }
                    i = t.call(e, a);
                } catch (e) {
                    i = [ 6, e ], o = 0;
                } finally {
                    r = n = 0;
                }
                if (5 & i[0]) throw i[1];
                return {
                    value: i[0] ? i[1] : void 0,
                    done: !0
                };
            }
        };
        Object.defineProperty(o, "__esModule", {
            value: !0
        });
        var u = t("../../../Script/ResKit/ResMgr"), l = t("../../../Script/Data/DataMgr"), s = t("../../../Script/Utils/DateUtils"), f = t("../../../Script/Audio/AudioMgr"), d = t("../../../Script/Manager/CommonMgr"), h = t("../../../Script/Event/EventMgr"), v = t("../../../Script/Config/Enum"), g = t("../../../Script/Event/EventEnum"), y = t("../../../Script/Manager/OnlineMgr"), m = t("../../../Script/View/QueuePopMgr"), S = t("../../../Script/Data/DataServerMgr"), w = t("../../../Script/View/pages/comps/ComShopPay"), b = t("../../../Script/View/base/BaseAnimPopwin"), M = t("../../../Script/View/common/ImageLabelNew1"), _ = t("../../../Script/Manager/SubpackageMgr"), E = cc._decorator, R = E.ccclass, P = E.property, C = function(e) {
            function t() {
                var t = null !== e && e.apply(this, arguments) || this;
                return t.prefabPath = "./prefab/popwin_gift_level", t.nodReward = [], t.onPay = null, 
                t.txtTime = null, t.txtLevel = null, t;
            }
            var r;
            return i(t, e), r = t, t.prototype.onOpen = function(t) {
                e.prototype.onOpen.call(this, t), this.shopId = S.DataServerMgr.LevelGift.ShopCfg.id, 
                this.initUi();
            }, t.prototype.onTouchMask = function() {
                this.onBack();
            }, t.prototype.onClose = function() {
                m.QueuePopMgr.I.removeFromList(), e.prototype.onClose.call(this);
            }, t.prototype.onClosed = function() {
                e.prototype.onClosed.call(this), u.ResMgr.releaseRef(this), _.default.I.releaseBundle(_.AssetBundleName.GiftLevel);
            }, t.prototype.addEvent = function() {
                e.prototype.addEvent.call(this), h.EventMgr.on(g.EventEnum.DoEventPerSecond, this.handleSetTime, this);
            }, t.prototype.removeEvent = function() {
                e.prototype.removeEvent.call(this), h.EventMgr.off(g.EventEnum.DoEventPerSecond, this.handleSetTime, this);
            }, Object.defineProperty(t.prototype, "isRelease", {
                get: function() {
                    return !0;
                },
                enumerable: !1,
                configurable: !0
            }), Object.defineProperty(t.prototype, "BannerOpen", {
                get: function() {
                    return y.OnlineMgr.getBanner(this.prefabPath);
                },
                enumerable: !1,
                configurable: !0
            }), t.prototype.initUi = function() {
                this.initReward(), this.txtLevel.setText("" + S.DataServerMgr.LevelGift.SuitLevel);
            }, t.prototype.initReward = function() {
                var e = l.DataMgr.Shop.getById(this.shopId);
                this.onPay.initWith(e);
                for (var t = 0; t < this.nodReward.length; t++) {
                    var r = this.nodReward[t], o = e.item[t][1], n = e.item[t][0], i = r.getChildByName("money").getComponent(M.default);
                    if (n == v.DropItemType.Buff) {
                        var a = o % 1e3;
                        i.setText(a + "m");
                    } else 1 != o && i.setText("" + o);
                    this.loadSprite(r.getChildByName("icon").getChildByName("img").getComponent(cc.Sprite), e.item[t]);
                }
            }, t.prototype.loadSprite = function(e, t) {
                return p(this, void 0, void 0, function() {
                    var r, o, n = this;
                    return c(this, function() {
                        return r = t[0], o = r, r == v.DropItemType.Buff ? (o = Math.floor(t[1] / 1e3), 
                        [ 2, d.default.I.loadBuffIcon(o + "").then(function(t) {
                            if (cc.isValid(e.getComponent)) {
                                e.spriteFrame = t;
                                var r = e.spriteFrame.getRect(), o = r.height / r.width;
                                o <= 1 ? (e.node.width = 240, e.node.height = 240 * o) : (e.node.height = 240, e.node.width = 240 / o), 
                                u.ResMgr.retainRefAsset(n, t);
                            }
                        }) ]) : [ 2, d.default.I.loadItemImg(o + "").then(function(t) {
                            if (cc.isValid(e.getComponent)) {
                                e.spriteFrame = t;
                                var r = 240, i = 240;
                                o == v.DropItemType.Diamond && (r = 220, i = 220), o == v.DropItemType.Energy && (r = 210, 
                                i = 210);
                                var a = e.spriteFrame.getRect(), p = a.height / a.width;
                                p <= 1 ? (e.node.width = r, e.node.height = r * p) : (e.node.height = i, e.node.width = i / p), 
                                u.ResMgr.retainRefAsset(n, t);
                            }
                        }) ];
                    });
                });
            }, t.prototype.onBack = function() {
                f.AudioMgr.button(), this.handleClose();
            }, t.prototype.handleSetTime = function() {
                var e = S.DataServerMgr.LevelGift.EndTime, t = s.default.now;
                e && e > t ? this.txtTime.string = s.default.getFormatBySecond9(e - t) : this.handleClose();
            }, t.prototype.handleClose = function(e) {
                void 0 === e && (e = !0), this.doClose(r, e);
            }, a([ P(cc.Node) ], t.prototype, "nodReward", void 0), a([ P(w.default) ], t.prototype, "onPay", void 0), 
            a([ P(cc.Label) ], t.prototype, "txtTime", void 0), a([ P(M.default) ], t.prototype, "txtLevel", void 0), 
            r = a([ R ], t);
        }(b.default);
        o.default = C, cc._RF.pop();
    }, {
        "../../../Script/Audio/AudioMgr": void 0,
        "../../../Script/Config/Enum": void 0,
        "../../../Script/Data/DataMgr": void 0,
        "../../../Script/Data/DataServerMgr": void 0,
        "../../../Script/Event/EventEnum": void 0,
        "../../../Script/Event/EventMgr": void 0,
        "../../../Script/Manager/CommonMgr": void 0,
        "../../../Script/Manager/OnlineMgr": void 0,
        "../../../Script/Manager/SubpackageMgr": void 0,
        "../../../Script/ResKit/ResMgr": void 0,
        "../../../Script/Utils/DateUtils": void 0,
        "../../../Script/View/QueuePopMgr": void 0,
        "../../../Script/View/base/BaseAnimPopwin": void 0,
        "../../../Script/View/common/ImageLabelNew1": void 0,
        "../../../Script/View/pages/comps/ComShopPay": void 0
    } ]
}, {}, [ "LevelGiftPopwin" ]);