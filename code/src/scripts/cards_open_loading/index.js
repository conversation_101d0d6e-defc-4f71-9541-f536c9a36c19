var e = require("../../../@babel/runtime/helpers/typeof");

window.__require = function e(r, t, a) {
    function n(i, p) {
        if (!t[i]) {
            if (!r[i]) {
                var c = i.split("/");
                if (c = c[c.length - 1], !r[c]) {
                    var d = "function" == typeof __require && __require;
                    if (!p && d) return d(c, !0);
                    if (o) return o(c, !0);
                    throw new Error("Cannot find module '" + i + "'");
                }
                i = c;
            }
            var u = t[i] = {
                exports: {}
            };
            r[i][0].call(u.exports, function(e) {
                return n(r[i][1][e] || e);
            }, u, u.exports, e, r, t, a);
        }
        return t[i].exports;
    }
    for (var o = "function" == typeof __require && __require, i = 0; i < a.length; i++) n(a[i]);
    return n;
}({
    CardsOpeningPopwin: [ function(r, t, a) {
        cc._RF.push(t, "4a6a0NrbANBHbzkf2osFGGR", "CardsOpeningPopwin");
        var n, o = this && this.__extends || (n = function(e, r) {
            return (n = Object.setPrototypeOf || {
                __proto__: []
            } instanceof Array && function(e, r) {
                e.__proto__ = r;
            } || function(e, r) {
                for (var t in r) Object.prototype.hasOwnProperty.call(r, t) && (e[t] = r[t]);
            })(e, r);
        }, function(e, r) {
            function t() {
                this.constructor = e;
            }
            n(e, r), e.prototype = null === r ? Object.create(r) : (t.prototype = r.prototype, 
            new t());
        }), i = this && this.__decorate || function(r, t, a, n) {
            var o, i = arguments.length, p = i < 3 ? t : null === n ? n = Object.getOwnPropertyDescriptor(t, a) : n;
            if ("object" == ("undefined" == typeof Reflect ? "undefined" : e(Reflect)) && "function" == typeof Reflect.decorate) p = Reflect.decorate(r, t, a, n); else for (var c = r.length - 1; c >= 0; c--) (o = r[c]) && (p = (i < 3 ? o(p) : i > 3 ? o(t, a, p) : o(t, a)) || p);
            return i > 3 && p && Object.defineProperty(t, a, p), p;
        };
        Object.defineProperty(a, "__esModule", {
            value: !0
        });
        var p = r("../../../Script/Common/Action"), c = r("../../../Script/View/UIMgr"), d = r("../../../Script/Data/DataMgr"), u = r("../../../Script/Manager/CommonMgr"), s = r("../../../Script/Manager/OnlineMgr"), l = r("../../../Script/View/base/BasePopwin"), f = r("../../../Script/Manager/SubpackageMgr"), g = r("../../../Script/Data/model/CardSystem/cardRewardMgr/CardMar"), h = r("../../../Script/Data/DataServerMgr"), y = cc._decorator, m = y.ccclass, v = y.property, C = function(e) {
            function r() {
                var r = null !== e && e.apply(this, arguments) || this;
                return r.prefabPath = "./prefab/popwin_cards_open_loading", r.nodeimg = null, r.cardQueueType = g.CardQueueType.CardQueueTypeRewards, 
                r.time = 0, r;
            }
            var t;
            return o(r, e), t = r, r.prototype.onOpen = function(r) {
                var t, a, n = this;
                if (e.prototype.onOpen.call(this, r), this.cardQueueType = (null === (t = r.data) || void 0 === t ? void 0 : t.cardQueueType) || g.CardQueueType.CardQueueTypeRewards, 
                this.time = 0, d.DataMgr.User.CardPackC2SList.length <= 0) this.handleClose(); else {
                    var o = d.DataMgr.User.CardPackC2SList[0][0] + "", i = (null === (a = h.DataServerMgr.Card.getNormalPackByVolcanoPackId(+o)) || void 0 === a ? void 0 : a.id) || o;
                    u.default.I.loadCardPackIcon(i + "", this).then(function(e) {
                        cc.isValid(n.nodeimg) && (n.nodeimg.active = !0, n.nodeimg.getComponent(cc.Sprite).spriteFrame = e);
                    });
                }
            }, r.prototype.update = function(e) {
                this.time >= 0 && (this.time += e), this.time >= 10 && (c.UIMgr.I.Open("cards_network_error"), 
                this.time = -1), d.DataMgr.User.CardPackC2SList.length <= 0 && (this.handleClose(), 
                this.time = -1);
            }, r.prototype.openAnim = function() {
                return p.default.playAnim(this.node);
            }, Object.defineProperty(r.prototype, "isRelease", {
                get: function() {
                    return !1;
                },
                enumerable: !1,
                configurable: !0
            }), Object.defineProperty(r.prototype, "BannerOpen", {
                get: function() {
                    return s.OnlineMgr.getBanner(this.prefabPath);
                },
                enumerable: !1,
                configurable: !0
            }), r.prototype.handleClose = function() {
                this.showCardsOpen(), this.doClose(t, !1);
            }, r.prototype.showCardsOpen = function() {
                var e = g.default.I.getTempCardPackRewards(this.cardQueueType);
                e && c.UIMgr.I.UnShift(f.AssetBundleName.cardsOpencard, {
                    data: {
                        rewardDatas: e,
                        from: this.cardQueueType
                    }
                });
            }, i([ v(cc.Node) ], r.prototype, "nodeimg", void 0), t = i([ m ], r);
        }(l.default);
        a.default = C, cc._RF.pop();
    }, {
        "../../../Script/Common/Action": void 0,
        "../../../Script/Data/DataMgr": void 0,
        "../../../Script/Data/DataServerMgr": void 0,
        "../../../Script/Data/model/CardSystem/cardRewardMgr/CardMar": void 0,
        "../../../Script/Manager/CommonMgr": void 0,
        "../../../Script/Manager/OnlineMgr": void 0,
        "../../../Script/Manager/SubpackageMgr": void 0,
        "../../../Script/View/UIMgr": void 0,
        "../../../Script/View/base/BasePopwin": void 0
    } ]
}, {}, [ "CardsOpeningPopwin" ]);