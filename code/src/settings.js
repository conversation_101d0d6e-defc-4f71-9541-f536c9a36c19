window._CCSettings = {
    platform: "wechatgame",
    groupList: [ "default", "UI", "Game", "wall", "bg", "Game2", "curtain", "effect", "top", "BigMap", "3D" ],
    collisionMatrix: [ [ !0, !0, !0, !0, null, null, null, null, null, null, !0 ], [ !0, !0, !0, !0, null, null, null, null, null, null, !0 ], [ !0, !0, !0, !0, null, null, null, null, null, null, !1 ], [ !0, !0, !0, !1, null, null, null, null, null, null, !1 ], [ !1, !1, !1, !1, !1, null, null, null, null, null, !1 ], [ !1, !1, !1, !1, !1, !0, null, null, null, null, !1 ], [ !1, !1, !1, !1, !1, !1, !1, null, null, null, !1 ], [ !1, !1, !1, !1, !1, !1, !1, !1, null, null, !1 ], [ !1, !1, !1, !1, !1, !1, !1, !1, !1, null, !1 ], [ !1, !1, !1, !1, !1, !1, !1, !1, !1, !1, !1 ], [ !0, !0, !1, !1, !1, !1, !1, !1, !1, !1, !0 ] ],
    hasResourcesBundle: !0,
    hasStartSceneBundle: !0,
    remoteBundles: [ "resources", "paygift_pege_sea", "beach_main", "addgroup", "blank_popwin", "buff_lucky_empty", "buff_roomrush", "buff_volcano", "build_reward_box", "buy_stars", "energy_c", "gift_center_v161", "gift_video", "gm_opt_v161", "gm_reward_deliver", "gm_sheep", "gobuild", "menu", "month_card_3rd", "month_pay_buy_b", "month_signin_even", "month_signin_odd", "nopublic", "prop_cube", "rank", "recycling_coin", "reward_box", "reward_boxtips", "subscribe_tip", "task", "vibrate_gm", "vipgroup", "welcomepack", "welfare_center", "ad_card", "ad_card_start", "badge_info", "badge_main", "badge_tips", "buff_rabbit_flip", "buff_rabbit_open", "buff_smiling_interface", "buff_smilingface_party", "cardenergy_energy", "cardgacha_gacha_v161", "cardgacha_gachareward_v161", "cardgacha_renew_v161", "cardphotos_collect", "cardphotos_season", "cards_bigcard_v161", "cards_cardoverreward_v161", "cards_cardoverreward2", "cards_cardsover_v161", "cards_cardsover_dragon_v161", "cards_choose_v161", "cards_collect_v161", "cards_complete_v161", "cards_dragoncard_v161", "cards_exchange_v161", "cards_giveyes_v161", "cards_info_v161", "cards_main_v161", "cards_network_error", "cards_newcard_v161", "cards_open_loading", "cards_openbox_v161", "cards_preview_v161", "cards_preview_dragon_v161", "cards_save_v161", "cards_tipinfo_v161", "cards_wish_v161", "chapter_main", "chapter_preview", "chess_collection_atlas", "chess_collection_info", "chess_collection_main", "chess_collection_open", "chess_collection_open2", "chess_collection_over", "chess_collection_raffle", "chess_collection_tip", "crazyblock_level", "crazyblock_play", "cupclear_level", "cupclear_play", "daily_roll", "daily_roll_show", "friend_info_v161", "friend_invite_v161", "friend_main_v161", "friend_note_v161", "friend_ok_v161", "gift_collectgame", "gift_desktop", "gift_diamond_wantall", "gift_endless_score", "gift_endless_super", "gift_level", "guide_bubble", "guide_unlock", "hardorder_chest", "hardorder_open", "passport_level_open", "passport_level_over", "passport_level_view", "passport_season_over", "passport_cupid_finish", "passport_cupid_main", "passport_cupid_pay", "passport_cupid_pay_info", "passport_cupid_taskover", "passport_kda_finish", "passport_kda_main", "passport_kda_pay", "passport_kda_pay_info", "passport_kda_taskover", "passport_third_finish", "passport_third_main", "passport_third_pay", "passport_third_pay_info", "passport_third_taskover", "beach_paygift", "garden_paygift", "paygift_3pack", "paygift_actcake_v161", "paygift_bargain", "paygift_blindbox", "paygift_candy", "paygift_catgarden", "paygift_childrenday", "paygift_coffee", "paygift_diamondminer_v161", "paygift_dig", "paygift_disco", "paygift_eaves", "paygift_endless", "paygift_enjoysilk_v161", "paygift_fan", "paygift_ferriswheel", "paygift_flower", "paygift_fragrance", "paygift_goddess", "paygift_honey", "paygift_lantern", "paygift_lanternhigh_v161", "paygift_newerwantall", "paygift_pege", "paygift_progressive", "paygift_queen", "paygift_recharge", "paygift_s10huafei", "paygift_s10qianying", "paygift_s10wuxie", "paygift_s11cold", "paygift_s11friend", "paygift_s11warmsun", "paygift_s12snownight", "paygift_s12staregg", "paygift_s12xiehou", "paygift_s13money", "paygift_s13snake", "paygift_s13wufu", "paygift_s3fool", "paygift_s3green", "paygift_s3sunflower", "paygift_s3water", "paygift_s4bike", "paygift_s4boxing", "paygift_s4camp", "paygift_s4coaster", "paygift_s4food", "paygift_s4ootd", "paygift_s4school", "paygift_s5birthday", "paygift_s5deer", "paygift_s5jasmine", "paygift_s5justyou", "paygift_s5mom", "paygift_s5playground", "paygift_s5roses", "paygift_s5sailing", "paygift_s6around", "paygift_s6begonia", "paygift_s6duanwu", "paygift_s6fireworks", "paygift_s6panda", "paygift_s7flower", "paygift_s7hydrangeas", "paygift_s7pearls", "paygift_s7qixi", "paygift_s7sea", "paygift_s7summer", "paygift_s7watermelon", "paygift_s8operationkey", "paygift_s9coffee", "paygift_s9jianjia", "paygift_s9juhua", "paygift_s9magickey", "paygift_savenewcard", "paygift_skykoi_v161", "paygift_snowman_v161", "paygift_springletter_v161", "paygift_surenew1_v161", "paygift_wantall", "paygift_warmlight", "paygift_wildcard", "paygift_wintermoon_v161", "player_badge_reward", "pushball_level", "pushball_play", "restroom_level", "restroom_play", "return_gift_letter", "return_gift_main", "royal_leave", "royal_level", "royal_play", "royal_result", "russia_level", "savegirl_leave", "savegirl_level", "savegirl_play", "savegirl_result", "secret_protocol", "day_sign", "return_sign", "storymap_map", "storymap_open", "storymap_tip", "wildcard_exchange", "wildcard_exchange_got", "wildcard_exchange_tip", "wildcard_reward", "newyear_reward_v161", "penguin_again", "penguin_confirm_v161", "penguin_info_v161", "penguin_join_v161", "penguin_main_v161", "penguin_point", "penguin_revive_v161", "penguin_reward_v161", "penguin_start_v161", "pet_main", "pet_reward", "retro_library_main", "retro_library_reward", "snowman_main_v161", "snowman_reward_v161", "spring_main", "spring_reward", "stream_rest_main", "stream_rest_reward", "stream_rest_start", "summer_night_main", "summer_night_reward", "sweetheart_main", "sweetheart_reward", "volcano_again_v161", "volcano_confirm_v161", "volcano_info_v161", "volcano_join_v161", "volcano_main_v161", "volcano_point", "volcano_revive_v161", "volcano_reward_v161", "volcano_start_v161", "cardsrecord_record", "airship_confirm", "airship_fail", "airship_info", "airship_main", "airship_revive", "airship_reward", "airship_start", "detective_info", "detective_main", "detective_pay_info", "detective_pay_view", "detective_paygift", "detective_reward", "detective_start", "helicopter_info", "helicopter_main", "helicopter_reward", "helicopter_round", "helicopter_start", "sabc_round", "sabc_start", "secondboard_info", "secondboard_main", "secondboard_newchess", "secondboard_paygift", "secondboard_rank", "secondboard_rank_reward", "secondboard_reward", "sheep_confirm", "sheep_energy", "sheep_fail", "sheep_finish", "sheep_game_info", "sheep_main", "sheep_main_info", "sheep_over", "sheep_paygift", "sheep_props", "sheep_rank", "sheep_reward", "sheep_start", "sheep_win", "super_egg_gacha", "super_egg_info", "super_egg_list", "super_egg_main", "super_egg_preview", "super_egg_reward", "super_egg_tip", "treasure_box_confirm", "treasure_box_fail", "treasure_box_info", "treasure_box_main", "treasure_box_paygift", "treasure_box_reward", "treasure_box_start", "treasure_hunt_change", "treasure_hunt_info", "treasure_hunt_main", "treasure_hunt_paygift", "treasure_hunt_reward", "treasure_magic_challenge", "treasure_magic_confirm", "treasure_magic_fail", "treasure_magic_fail_confirm", "treasure_magic_info", "treasure_magic_main_a", "treasure_magic_paygift", "treasure_magic_reward", "treasure_magic_start", "cake_info_v161", "cake_invite_v161", "cake_lottery_v161", "cake_main_v161", "cake_start_v161", "partner_reward_info", "beach_chess", "beach_info", "beach_points", "beach_reward", "garden_chess", "garden_info", "garden_main", "garden_points", "garden_reward", "sweet101_info", "sweet101_main", "sweet101_reward", "sweet101_unlock", "coconut_info", "coconut_main", "coconut_reward", "coconut_unlock", "dingdang_fish_info", "dingdang_fish_main", "dingdang_fish_reward", "dingdang_fish_unlock", "dingdang_yoga_info", "dingdang_yoga_main", "dingdang_yoga_reward", "dingdang_yoga_unlock", "lucky_gewen_info", "lucky_gewen_main", "lucky_gewen_reward", "lucky_gewen_unlock", "robot_pengpeng_info", "robot_pengpeng_main", "robot_pengpeng_reward", "robot_pengpeng_unlock", "robot_work_info", "robot_work_main", "robot_work_reward", "robot_work_unlock", "sport_main", "sport_reward", "sport_unlock", "spring_picnic_info", "spring_picnic_main", "spring_picnic_reward", "spring_picnic_unlock", "sweetspring_main", "sweetspring_reward", "sweetspring_unlock", "weekend_gewen_info", "weekend_gewen_main", "weekend_gewen_reward", "weekend_gewen_unlock", "pege_info", "pege_main", "pege_progressive", "pege_reward", "pege_sea_info", "pege_sea_main", "pege_sea_progressive", "pege_sea_reward", "cards_main_dargon_v161", "dinner_prom_main", "dinner_prom_info", "dinner_prom_pick", "dinner_prom_point", "activity_short_tipinfo", "beach_party_main", "beach_party_reward", "blocks_main", "blocks_reward", "dinner_info", "dinner_main", "dinner_pick", "dinner_point", "colormania_main", "colormania_reward", "corgi_dog_main", "corgi_dog_reward", "corgi_dog_start", "dinner_winter_info", "dinner_winter_main", "dinner_winter_pick", "dinner_winter_point", "dumpling_main", "dumpling_reward", "funpark_main", "funpark_reward", "hydrangeas_main", "hydrangeas_reward", "hydrangeas_start", "motorboat_main", "motorboat_reward", "newyear_main_v161", "winter_dinner_pick", "winter_dinner_point", "winter_dinner_info", "winter_dinner_main" ],
    subpackages: [ "main_scripts", "ab_scripts", "adv_test", "bind_phone", "buff", "buff_goldenegg_boom", "choice_pack", "double_energy_info", "energy_lack", "game_circle", "gm_new", "gm_old", "level_view", "mail", "map_change", "network_error", "open_box", "setting", "story", "subscribe", "task_build_info", "temp_desktop", "activity1_main", "activity1_notice", "activity1_open", "activity_over", "activity_rank", "activity_rank_info", "activity_rank_reward", "buy_cat", "buy_new", "buy_queencard", "cards_opencard", "champion_info", "champion_join", "champion_rank", "champion_reward", "champion_task_hide", "champion_tip", "clone_event_info1", "clone_event_info2", "item_selltip", "dress_main", "dress_notice", "gift_endless", "gift_free", "gift_growth", "gift_pig", "gift_rebate", "michelin_info", "michelin_rank", "michelin_reward", "michelin_select", "month_pay_buy", "month_pay_info", "notice_secure", "passport_lvup_tip", "passport_tipinfo", "player_badge_list", "player_badge_show", "player_head", "player_help", "player_info", "rabbit_back", "rabbit_guide", "rabbit_help", "rabbit_main", "rabbit_rank", "rabbit_rule", "rabbit_shop", "block", "dart", "num", "ui_entry", "ui_over", "activity_plot1", "activity_plot1_over", "activity_plot1_pay", "activity_plot_continue" ],
    launchScene: "db://assets/Scene/Loading/LoadScene.fire",
    orientation: "",
    server: "https://qzz2d.qzzres.com/merge_sweeter",
    jsList: [ "assets/libs/aesjs/aes.min.js" ],
    bundleVers: {
        internal: "e6604",
        main_scripts: "5dca4",
        resources: "776ca",
        ab_scripts: "1d753",
        paygift_pege_sea: "0bd3c",
        "start-scene": "b5b6b",
        main: "1a77c",
        beach_main: "d4656",
        addgroup: "0bbba",
        adv_test: "52ef0",
        bind_phone: "edb0f",
        blank_popwin: "ef19c",
        buff: "db472",
        buff_goldenegg_boom: "82171",
        buff_lucky_empty: "2ce79",
        buff_roomrush: "59687",
        buff_volcano: "ce765",
        build_reward_box: "2b1c8",
        buy_stars: "1a1a3",
        choice_pack: "b875e",
        double_energy_info: "b1025",
        energy_c: "550c5",
        energy_lack: "36e96",
        game_circle: "70998",
        gift_center_v161: "c4709",
        gift_video: "b2bf9",
        gm_new: "6b7cf",
        gm_old: "1fdd7",
        gm_opt_v161: "589ba",
        gm_reward_deliver: "b2bed",
        gm_sheep: "b3c8a",
        gobuild: "7f6b5",
        level_view: "fdd6b",
        mail: "c4c97",
        map_change: "a9606",
        menu: "a1407",
        month_card_3rd: "88ea5",
        month_pay_buy_b: "5a835",
        month_signin_even: "9055f",
        month_signin_odd: "22f97",
        network_error: "6cd14",
        nopublic: "3acd2",
        open_box: "c0abb",
        prop_cube: "589de",
        rank: "372f4",
        recycling_coin: "59c0a",
        reward_box: "b49bc",
        reward_boxtips: "6500a",
        setting: "97130",
        story: "9ff6a",
        subscribe: "3c36b",
        subscribe_tip: "c4a7f",
        task: "b1394",
        task_build_info: "0cb8c",
        temp_desktop: "d84a7",
        vibrate_gm: "bdf63",
        vipgroup: "19af2",
        welcomepack: "e1efa",
        welfare_center: "9bad1",
        activity1_main: "f7a71",
        activity1_notice: "3b675",
        activity1_open: "eb9fd",
        activity_over: "aae76",
        activity_rank: "abc73",
        activity_rank_info: "a3e0a",
        activity_rank_reward: "d801e",
        ad_card: "0391b",
        ad_card_start: "c0c6b",
        badge_info: "3416e",
        badge_main: "4acf5",
        badge_tips: "4d4b2",
        buff_rabbit_flip: "cddbf",
        buff_rabbit_open: "6c760",
        buff_smiling_interface: "2fe53",
        buff_smilingface_party: "5b493",
        buy_cat: "2554c",
        buy_new: "784e3",
        buy_queencard: "173b9",
        cardenergy_energy: "1ac66",
        cardgacha_gacha_v161: "ef52e",
        cardgacha_gachareward_v161: "17b68",
        cardgacha_renew_v161: "dd3b0",
        cardphotos_collect: "bca3b",
        cardphotos_season: "071e4",
        cards_bigcard_v161: "a2820",
        cards_cardoverreward_v161: "96182",
        cards_cardoverreward2: "36bf9",
        cards_cardsover_v161: "247b1",
        cards_cardsover_dragon_v161: "1008b",
        cards_choose_v161: "d59ec",
        cards_collect_v161: "82a4b",
        cards_complete_v161: "eb4de",
        cards_dragoncard_v161: "ddfe6",
        cards_exchange_v161: "0b059",
        cards_giveyes_v161: "f61bb",
        cards_info_v161: "a10f7",
        cards_main_v161: "c1a27",
        cards_network_error: "4bbcc",
        cards_newcard_v161: "59701",
        cards_open_loading: "11b72",
        cards_openbox_v161: "d3391",
        cards_opencard: "3c2a8",
        cards_preview_v161: "90db8",
        cards_preview_dragon_v161: "2919a",
        cards_save_v161: "70f8d",
        cards_tipinfo_v161: "84c66",
        cards_wish_v161: "a784f",
        champion_info: "a10f3",
        champion_join: "a6805",
        champion_rank: "5e43e",
        champion_reward: "09d03",
        champion_task_hide: "7f571",
        champion_tip: "1dc28",
        chapter_main: "1d119",
        chapter_preview: "bffa0",
        chess_collection_atlas: "d3107",
        chess_collection_info: "6e029",
        chess_collection_main: "eb54d",
        chess_collection_open: "69f70",
        chess_collection_open2: "55701",
        chess_collection_over: "32855",
        chess_collection_raffle: "968b1",
        chess_collection_tip: "947ae",
        clone_event_info1: "29b52",
        clone_event_info2: "e45ad",
        item_selltip: "17873",
        crazyblock_level: "aebe9",
        crazyblock_play: "110fe",
        cupclear_level: "e5417",
        cupclear_play: "ab157",
        daily_roll: "bbd99",
        daily_roll_show: "41d2a",
        dress_main: "3b69d",
        dress_notice: "a58bc",
        friend_info_v161: "230e0",
        friend_invite_v161: "79c2e",
        friend_main_v161: "b8b72",
        friend_note_v161: "1f2d6",
        friend_ok_v161: "ef2ce",
        gift_collectgame: "5f9be",
        gift_desktop: "92fed",
        gift_diamond_wantall: "92de2",
        gift_endless: "9d1b6",
        gift_endless_score: "4c07c",
        gift_endless_super: "93608",
        gift_free: "a7fca",
        gift_growth: "4eea3",
        gift_level: "f1a95",
        gift_pig: "73e66",
        gift_rebate: "08715",
        guide_bubble: "f4d1d",
        guide_unlock: "31892",
        hardorder_chest: "9addc",
        hardorder_open: "2d6dd",
        michelin_info: "38909",
        michelin_rank: "c6842",
        michelin_reward: "8a344",
        michelin_select: "005fb",
        month_pay_buy: "f9cb9",
        month_pay_info: "54ced",
        notice_secure: "25838",
        passport_lvup_tip: "536f1",
        passport_tipinfo: "d714d",
        passport_level_open: "286e6",
        passport_level_over: "7ca46",
        passport_level_view: "acaac",
        passport_season_over: "412be",
        passport_cupid_finish: "02b46",
        passport_cupid_main: "01ab3",
        passport_cupid_pay: "f7cea",
        passport_cupid_pay_info: "dca96",
        passport_cupid_taskover: "11502",
        passport_kda_finish: "f04f0",
        passport_kda_main: "c800c",
        passport_kda_pay: "84c40",
        passport_kda_pay_info: "2dcb3",
        passport_kda_taskover: "38812",
        passport_third_finish: "5c8ee",
        passport_third_main: "e2e49",
        passport_third_pay: "0dca0",
        passport_third_pay_info: "36eec",
        passport_third_taskover: "9bf7b",
        beach_paygift: "5cd70",
        garden_paygift: "c07fe",
        paygift_3pack: "d56f9",
        paygift_actcake_v161: "e6468",
        paygift_bargain: "85934",
        paygift_blindbox: "7d293",
        paygift_candy: "d2a19",
        paygift_catgarden: "f3b84",
        paygift_childrenday: "c4ac8",
        paygift_coffee: "2af55",
        paygift_diamondminer_v161: "53476",
        paygift_dig: "39452",
        paygift_disco: "f157b",
        paygift_eaves: "3c0aa",
        paygift_endless: "f1a34",
        paygift_enjoysilk_v161: "11e23",
        paygift_fan: "9d04d",
        paygift_ferriswheel: "d573f",
        paygift_flower: "fbe09",
        paygift_fragrance: "3684b",
        paygift_goddess: "cd1d7",
        paygift_honey: "bcd86",
        paygift_lantern: "5cb0e",
        paygift_lanternhigh_v161: "40a67",
        paygift_newerwantall: "65a1b",
        paygift_pege: "c5286",
        paygift_progressive: "08cd2",
        paygift_queen: "f4b59",
        paygift_recharge: "1d78c",
        paygift_s10huafei: "ea289",
        paygift_s10qianying: "75439",
        paygift_s10wuxie: "0236a",
        paygift_s11cold: "362a6",
        paygift_s11friend: "6cd5e",
        paygift_s11warmsun: "c5d9c",
        paygift_s12snownight: "9ad67",
        paygift_s12staregg: "cfafd",
        paygift_s12xiehou: "d7d19",
        paygift_s13money: "2e518",
        paygift_s13snake: "79465",
        paygift_s13wufu: "e633e",
        paygift_s3fool: "85a4c",
        paygift_s3green: "b0a3b",
        paygift_s3sunflower: "70e74",
        paygift_s3water: "e2637",
        paygift_s4bike: "018d3",
        paygift_s4boxing: "9b51a",
        paygift_s4camp: "08edb",
        paygift_s4coaster: "ea7c9",
        paygift_s4food: "3463d",
        paygift_s4ootd: "49bdc",
        paygift_s4school: "fc66e",
        paygift_s5birthday: "cf3c7",
        paygift_s5deer: "4787a",
        paygift_s5jasmine: "a5e70",
        paygift_s5justyou: "8099f",
        paygift_s5mom: "6128b",
        paygift_s5playground: "a89c4",
        paygift_s5roses: "f5041",
        paygift_s5sailing: "ff74e",
        paygift_s6around: "db6e6",
        paygift_s6begonia: "1defb",
        paygift_s6duanwu: "ea0fd",
        paygift_s6fireworks: "8d22e",
        paygift_s6panda: "8b112",
        paygift_s7flower: "6a334",
        paygift_s7hydrangeas: "2cab8",
        paygift_s7pearls: "9cb69",
        paygift_s7qixi: "9f693",
        paygift_s7sea: "b3852",
        paygift_s7summer: "b752b",
        paygift_s7watermelon: "5bfc8",
        paygift_s8operationkey: "645bd",
        paygift_s9coffee: "06e49",
        paygift_s9jianjia: "04887",
        paygift_s9juhua: "db365",
        paygift_s9magickey: "4aef2",
        paygift_savenewcard: "2c34e",
        paygift_skykoi_v161: "f3034",
        paygift_snowman_v161: "5764d",
        paygift_springletter_v161: "8eda2",
        paygift_surenew1_v161: "6b642",
        paygift_wantall: "0f155",
        paygift_warmlight: "4b75d",
        paygift_wildcard: "b0279",
        paygift_wintermoon_v161: "f3871",
        player_badge_list: "b3022",
        player_badge_reward: "72fe3",
        player_badge_show: "c1e78",
        player_head: "a1877",
        player_help: "2a106",
        player_info: "9d958",
        pushball_level: "c264e",
        pushball_play: "99ee6",
        rabbit_back: "6f2be",
        rabbit_guide: "ee1ed",
        rabbit_help: "d6e43",
        rabbit_main: "66e97",
        rabbit_rank: "84a22",
        rabbit_rule: "c0c00",
        rabbit_shop: "68d18",
        restroom_level: "47c92",
        restroom_play: "f4339",
        return_gift_letter: "35927",
        return_gift_main: "93601",
        royal_leave: "6f9c0",
        royal_level: "dac73",
        royal_play: "c3403",
        royal_result: "04ecf",
        russia_level: "d9363",
        savegirl_leave: "21483",
        savegirl_level: "8256d",
        savegirl_play: "f2dca",
        savegirl_result: "10ed7",
        secret_protocol: "dd328",
        day_sign: "de4ea",
        return_sign: "dd5e2",
        storymap_map: "8f3ab",
        storymap_open: "fb79f",
        storymap_tip: "98f9d",
        block: "743a0",
        dart: "aae27",
        num: "79da4",
        ui_entry: "992f2",
        ui_over: "c16d4",
        wildcard_exchange: "fab93",
        wildcard_exchange_got: "86de6",
        wildcard_exchange_tip: "34968",
        wildcard_reward: "c7cc1",
        newyear_reward_v161: "21156",
        penguin_again: "1b128",
        penguin_confirm_v161: "76469",
        penguin_info_v161: "73344",
        penguin_join_v161: "2cbda",
        penguin_main_v161: "c2f1a",
        penguin_point: "e4be5",
        penguin_revive_v161: "8445f",
        penguin_reward_v161: "f9bf2",
        penguin_start_v161: "c68cf",
        pet_main: "958de",
        pet_reward: "20c65",
        retro_library_main: "e4140",
        retro_library_reward: "71153",
        snowman_main_v161: "6a3be",
        snowman_reward_v161: "cd118",
        spring_main: "32552",
        spring_reward: "d58e6",
        stream_rest_main: "7f74a",
        stream_rest_reward: "53248",
        stream_rest_start: "8a8bc",
        summer_night_main: "b072d",
        summer_night_reward: "88700",
        sweetheart_main: "23a6c",
        sweetheart_reward: "d604e",
        volcano_again_v161: "357d2",
        volcano_confirm_v161: "2a4fb",
        volcano_info_v161: "68590",
        volcano_join_v161: "efd68",
        volcano_main_v161: "7e697",
        volcano_point: "78bd5",
        volcano_revive_v161: "1d311",
        volcano_reward_v161: "6bd11",
        volcano_start_v161: "8e08e",
        cardsrecord_record: "12859",
        airship_confirm: "ed964",
        airship_fail: "b47f0",
        airship_info: "9931a",
        airship_main: "388c9",
        airship_revive: "f7b64",
        airship_reward: "26eb4",
        airship_start: "d4e7a",
        detective_info: "8b0db",
        detective_main: "baee4",
        detective_pay_info: "b151c",
        detective_pay_view: "3a228",
        detective_paygift: "686c2",
        detective_reward: "d5656",
        detective_start: "78376",
        helicopter_info: "72eb5",
        helicopter_main: "45d08",
        helicopter_reward: "78925",
        helicopter_round: "223dc",
        helicopter_start: "36972",
        sabc_round: "eea0a",
        sabc_start: "dcc35",
        secondboard_info: "37dc2",
        secondboard_main: "b71a6",
        secondboard_newchess: "01a67",
        secondboard_paygift: "89ede",
        secondboard_rank: "c99e9",
        secondboard_rank_reward: "abb4c",
        secondboard_reward: "405b1",
        sheep_confirm: "ddb94",
        sheep_energy: "ee1f5",
        sheep_fail: "ff62e",
        sheep_finish: "42f3f",
        sheep_game_info: "13ce6",
        sheep_main: "abced",
        sheep_main_info: "94cc9",
        sheep_over: "6073d",
        sheep_paygift: "5ffbd",
        sheep_props: "c403b",
        sheep_rank: "9dc04",
        sheep_reward: "40aa0",
        sheep_start: "f642e",
        sheep_win: "02db6",
        super_egg_gacha: "ebeee",
        super_egg_info: "daf8d",
        super_egg_list: "416d3",
        super_egg_main: "6b99d",
        super_egg_preview: "829e3",
        super_egg_reward: "e62ad",
        super_egg_tip: "8e4df",
        treasure_box_confirm: "a261f",
        treasure_box_fail: "bc7c8",
        treasure_box_info: "52e09",
        treasure_box_main: "e2772",
        treasure_box_paygift: "0a186",
        treasure_box_reward: "75c00",
        treasure_box_start: "93d98",
        treasure_hunt_change: "60da7",
        treasure_hunt_info: "e9567",
        treasure_hunt_main: "ba6d0",
        treasure_hunt_paygift: "ea4ad",
        treasure_hunt_reward: "2f92f",
        treasure_magic_challenge: "7fca8",
        treasure_magic_confirm: "e7920",
        treasure_magic_fail: "17829",
        treasure_magic_fail_confirm: "d73b0",
        treasure_magic_info: "1c41e",
        treasure_magic_main_a: "ab19b",
        treasure_magic_paygift: "ae836",
        treasure_magic_reward: "d8e71",
        treasure_magic_start: "0aa37",
        cake_info_v161: "68bfc",
        cake_invite_v161: "b8cc7",
        cake_lottery_v161: "0f236",
        cake_main_v161: "6663b",
        cake_start_v161: "d942b",
        partner_reward_info: "e87e1",
        beach_chess: "37afc",
        beach_info: "577c5",
        beach_points: "2edd9",
        beach_reward: "0e9b6",
        garden_chess: "4b791",
        garden_info: "d0b1e",
        garden_main: "1a71c",
        garden_points: "6e78b",
        garden_reward: "537e3",
        sweet101_info: "35bd7",
        sweet101_main: "f4808",
        sweet101_reward: "e7bca",
        sweet101_unlock: "88295",
        coconut_info: "25d9b",
        coconut_main: "722b2",
        coconut_reward: "3685f",
        coconut_unlock: "dd72c",
        dingdang_fish_info: "68757",
        dingdang_fish_main: "95b39",
        dingdang_fish_reward: "d2703",
        dingdang_fish_unlock: "25ce1",
        dingdang_yoga_info: "9697b",
        dingdang_yoga_main: "a8f94",
        dingdang_yoga_reward: "dad39",
        dingdang_yoga_unlock: "fa944",
        lucky_gewen_info: "b8b95",
        lucky_gewen_main: "65784",
        lucky_gewen_reward: "0d452",
        lucky_gewen_unlock: "54a43",
        robot_pengpeng_info: "711c9",
        robot_pengpeng_main: "773f5",
        robot_pengpeng_reward: "c2516",
        robot_pengpeng_unlock: "97b78",
        robot_work_info: "aab15",
        robot_work_main: "6b1c9",
        robot_work_reward: "2ff0d",
        robot_work_unlock: "c35d8",
        sport_main: "00215",
        sport_reward: "ce839",
        sport_unlock: "607dc",
        spring_picnic_info: "e17be",
        spring_picnic_main: "f4040",
        spring_picnic_reward: "9a890",
        spring_picnic_unlock: "82ba0",
        sweetspring_main: "78da7",
        sweetspring_reward: "e6a84",
        sweetspring_unlock: "8fec8",
        weekend_gewen_info: "9ef73",
        weekend_gewen_main: "11146",
        weekend_gewen_reward: "9571c",
        weekend_gewen_unlock: "43dfe",
        pege_info: "76dc6",
        pege_main: "e1f9a",
        pege_progressive: "37080",
        pege_reward: "f4d91",
        pege_sea_info: "e478c",
        pege_sea_main: "55ed8",
        pege_sea_progressive: "0f5f1",
        pege_sea_reward: "489d0",
        cards_main_dargon_v161: "5e9c8",
        dinner_prom_main: "8e915",
        dinner_prom_info: "eab9d",
        dinner_prom_pick: "a3dc6",
        dinner_prom_point: "36c55",
        activity_short_tipinfo: "60aa4",
        beach_party_main: "2bc53",
        beach_party_reward: "781ce",
        blocks_main: "35ace",
        blocks_reward: "571a0",
        dinner_info: "e3b06",
        dinner_main: "30e6e",
        dinner_pick: "76cd6",
        dinner_point: "01a89",
        colormania_main: "ae44e",
        colormania_reward: "d4485",
        corgi_dog_main: "fca25",
        corgi_dog_reward: "c1e12",
        corgi_dog_start: "ee56e",
        dinner_winter_info: "e9932",
        dinner_winter_main: "b7969",
        dinner_winter_pick: "7df58",
        dinner_winter_point: "dcb6e",
        dumpling_main: "59650",
        dumpling_reward: "00a44",
        funpark_main: "96340",
        funpark_reward: "733a6",
        hydrangeas_main: "72279",
        hydrangeas_reward: "4dac0",
        hydrangeas_start: "40275",
        motorboat_main: "7b2b2",
        motorboat_reward: "8b86d",
        newyear_main_v161: "b9754",
        winter_dinner_pick: "5e74f",
        winter_dinner_point: "43f60",
        activity_plot1: "1f051",
        activity_plot1_over: "c06ce",
        activity_plot1_pay: "cdea7",
        activity_plot_continue: "1b900",
        winter_dinner_info: "16c15",
        winter_dinner_main: "75fc9"
    }
};