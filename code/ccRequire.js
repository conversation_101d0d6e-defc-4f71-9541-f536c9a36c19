var r = {
    "src/assets/libs/aesjs/aes.min.js": function() {
        return require("src/assets/libs/aesjs/aes.min.js");
    },
    "assets/internal/index.js": function() {
        return require("assets/internal/index.js");
    },
    "src/scripts/resources/index.js": function() {
        return require("src/scripts/resources/index.js");
    },
    "src/scripts/paygift_pege_sea/index.js": function() {
        return require("src/scripts/paygift_pege_sea/index.js");
    },
    "assets/start-scene/index.js": function() {
        return require("assets/start-scene/index.js");
    },
    "assets/main/index.js": function() {
        return require("assets/main/index.js");
    },
    "src/scripts/beach_main/index.js": function() {
        return require("src/scripts/beach_main/index.js");
    },
    "src/scripts/addgroup/index.js": function() {
        return require("src/scripts/addgroup/index.js");
    },
    "src/scripts/blank_popwin/index.js": function() {
        return require("src/scripts/blank_popwin/index.js");
    },
    "src/scripts/buff_lucky_empty/index.js": function() {
        return require("src/scripts/buff_lucky_empty/index.js");
    },
    "src/scripts/buff_roomrush/index.js": function() {
        return require("src/scripts/buff_roomrush/index.js");
    },
    "src/scripts/buff_volcano/index.js": function() {
        return require("src/scripts/buff_volcano/index.js");
    },
    "src/scripts/build_reward_box/index.js": function() {
        return require("src/scripts/build_reward_box/index.js");
    },
    "src/scripts/buy_stars/index.js": function() {
        return require("src/scripts/buy_stars/index.js");
    },
    "src/scripts/energy_c/index.js": function() {
        return require("src/scripts/energy_c/index.js");
    },
    "src/scripts/gift_center_v161/index.js": function() {
        return require("src/scripts/gift_center_v161/index.js");
    },
    "src/scripts/gift_video/index.js": function() {
        return require("src/scripts/gift_video/index.js");
    },
    "src/scripts/gm_opt_v161/index.js": function() {
        return require("src/scripts/gm_opt_v161/index.js");
    },
    "src/scripts/gm_reward_deliver/index.js": function() {
        return require("src/scripts/gm_reward_deliver/index.js");
    },
    "src/scripts/gm_sheep/index.js": function() {
        return require("src/scripts/gm_sheep/index.js");
    },
    "src/scripts/gobuild/index.js": function() {
        return require("src/scripts/gobuild/index.js");
    },
    "src/scripts/menu/index.js": function() {
        return require("src/scripts/menu/index.js");
    },
    "src/scripts/month_card_3rd/index.js": function() {
        return require("src/scripts/month_card_3rd/index.js");
    },
    "src/scripts/month_pay_buy_b/index.js": function() {
        return require("src/scripts/month_pay_buy_b/index.js");
    },
    "src/scripts/month_signin_even/index.js": function() {
        return require("src/scripts/month_signin_even/index.js");
    },
    "src/scripts/month_signin_odd/index.js": function() {
        return require("src/scripts/month_signin_odd/index.js");
    },
    "src/scripts/nopublic/index.js": function() {
        return require("src/scripts/nopublic/index.js");
    },
    "src/scripts/prop_cube/index.js": function() {
        return require("src/scripts/prop_cube/index.js");
    },
    "src/scripts/rank/index.js": function() {
        return require("src/scripts/rank/index.js");
    },
    "src/scripts/recycling_coin/index.js": function() {
        return require("src/scripts/recycling_coin/index.js");
    },
    "src/scripts/reward_box/index.js": function() {
        return require("src/scripts/reward_box/index.js");
    },
    "src/scripts/reward_boxtips/index.js": function() {
        return require("src/scripts/reward_boxtips/index.js");
    },
    "src/scripts/subscribe_tip/index.js": function() {
        return require("src/scripts/subscribe_tip/index.js");
    },
    "src/scripts/task/index.js": function() {
        return require("src/scripts/task/index.js");
    },
    "src/scripts/vibrate_gm/index.js": function() {
        return require("src/scripts/vibrate_gm/index.js");
    },
    "src/scripts/vipgroup/index.js": function() {
        return require("src/scripts/vipgroup/index.js");
    },
    "src/scripts/welcomepack/index.js": function() {
        return require("src/scripts/welcomepack/index.js");
    },
    "src/scripts/welfare_center/index.js": function() {
        return require("src/scripts/welfare_center/index.js");
    },
    "src/scripts/ad_card/index.js": function() {
        return require("src/scripts/ad_card/index.js");
    },
    "src/scripts/ad_card_start/index.js": function() {
        return require("src/scripts/ad_card_start/index.js");
    },
    "src/scripts/badge_info/index.js": function() {
        return require("src/scripts/badge_info/index.js");
    },
    "src/scripts/badge_main/index.js": function() {
        return require("src/scripts/badge_main/index.js");
    },
    "src/scripts/badge_tips/index.js": function() {
        return require("src/scripts/badge_tips/index.js");
    },
    "src/scripts/buff_rabbit_flip/index.js": function() {
        return require("src/scripts/buff_rabbit_flip/index.js");
    },
    "src/scripts/buff_rabbit_open/index.js": function() {
        return require("src/scripts/buff_rabbit_open/index.js");
    },
    "src/scripts/buff_smiling_interface/index.js": function() {
        return require("src/scripts/buff_smiling_interface/index.js");
    },
    "src/scripts/buff_smilingface_party/index.js": function() {
        return require("src/scripts/buff_smilingface_party/index.js");
    },
    "src/scripts/cardenergy_energy/index.js": function() {
        return require("src/scripts/cardenergy_energy/index.js");
    },
    "src/scripts/cardgacha_gacha_v161/index.js": function() {
        return require("src/scripts/cardgacha_gacha_v161/index.js");
    },
    "src/scripts/cardgacha_gachareward_v161/index.js": function() {
        return require("src/scripts/cardgacha_gachareward_v161/index.js");
    },
    "src/scripts/cardgacha_renew_v161/index.js": function() {
        return require("src/scripts/cardgacha_renew_v161/index.js");
    },
    "src/scripts/cardphotos_collect/index.js": function() {
        return require("src/scripts/cardphotos_collect/index.js");
    },
    "src/scripts/cardphotos_season/index.js": function() {
        return require("src/scripts/cardphotos_season/index.js");
    },
    "src/scripts/cards_bigcard_v161/index.js": function() {
        return require("src/scripts/cards_bigcard_v161/index.js");
    },
    "src/scripts/cards_cardoverreward_v161/index.js": function() {
        return require("src/scripts/cards_cardoverreward_v161/index.js");
    },
    "src/scripts/cards_cardoverreward2/index.js": function() {
        return require("src/scripts/cards_cardoverreward2/index.js");
    },
    "src/scripts/cards_cardsover_v161/index.js": function() {
        return require("src/scripts/cards_cardsover_v161/index.js");
    },
    "src/scripts/cards_cardsover_dragon_v161/index.js": function() {
        return require("src/scripts/cards_cardsover_dragon_v161/index.js");
    },
    "src/scripts/cards_choose_v161/index.js": function() {
        return require("src/scripts/cards_choose_v161/index.js");
    },
    "src/scripts/cards_collect_v161/index.js": function() {
        return require("src/scripts/cards_collect_v161/index.js");
    },
    "src/scripts/cards_complete_v161/index.js": function() {
        return require("src/scripts/cards_complete_v161/index.js");
    },
    "src/scripts/cards_dragoncard_v161/index.js": function() {
        return require("src/scripts/cards_dragoncard_v161/index.js");
    },
    "src/scripts/cards_exchange_v161/index.js": function() {
        return require("src/scripts/cards_exchange_v161/index.js");
    },
    "src/scripts/cards_giveyes_v161/index.js": function() {
        return require("src/scripts/cards_giveyes_v161/index.js");
    },
    "src/scripts/cards_info_v161/index.js": function() {
        return require("src/scripts/cards_info_v161/index.js");
    },
    "src/scripts/cards_main_v161/index.js": function() {
        return require("src/scripts/cards_main_v161/index.js");
    },
    "src/scripts/cards_network_error/index.js": function() {
        return require("src/scripts/cards_network_error/index.js");
    },
    "src/scripts/cards_newcard_v161/index.js": function() {
        return require("src/scripts/cards_newcard_v161/index.js");
    },
    "src/scripts/cards_open_loading/index.js": function() {
        return require("src/scripts/cards_open_loading/index.js");
    },
    "src/scripts/cards_openbox_v161/index.js": function() {
        return require("src/scripts/cards_openbox_v161/index.js");
    },
    "src/scripts/cards_preview_v161/index.js": function() {
        return require("src/scripts/cards_preview_v161/index.js");
    },
    "src/scripts/cards_preview_dragon_v161/index.js": function() {
        return require("src/scripts/cards_preview_dragon_v161/index.js");
    },
    "src/scripts/cards_save_v161/index.js": function() {
        return require("src/scripts/cards_save_v161/index.js");
    },
    "src/scripts/cards_tipinfo_v161/index.js": function() {
        return require("src/scripts/cards_tipinfo_v161/index.js");
    },
    "src/scripts/cards_wish_v161/index.js": function() {
        return require("src/scripts/cards_wish_v161/index.js");
    },
    "src/scripts/chapter_main/index.js": function() {
        return require("src/scripts/chapter_main/index.js");
    },
    "src/scripts/chapter_preview/index.js": function() {
        return require("src/scripts/chapter_preview/index.js");
    },
    "src/scripts/chess_collection_atlas/index.js": function() {
        return require("src/scripts/chess_collection_atlas/index.js");
    },
    "src/scripts/chess_collection_info/index.js": function() {
        return require("src/scripts/chess_collection_info/index.js");
    },
    "src/scripts/chess_collection_main/index.js": function() {
        return require("src/scripts/chess_collection_main/index.js");
    },
    "src/scripts/chess_collection_open/index.js": function() {
        return require("src/scripts/chess_collection_open/index.js");
    },
    "src/scripts/chess_collection_open2/index.js": function() {
        return require("src/scripts/chess_collection_open2/index.js");
    },
    "src/scripts/chess_collection_over/index.js": function() {
        return require("src/scripts/chess_collection_over/index.js");
    },
    "src/scripts/chess_collection_raffle/index.js": function() {
        return require("src/scripts/chess_collection_raffle/index.js");
    },
    "src/scripts/chess_collection_tip/index.js": function() {
        return require("src/scripts/chess_collection_tip/index.js");
    },
    "src/scripts/crazyblock_level/index.js": function() {
        return require("src/scripts/crazyblock_level/index.js");
    },
    "src/scripts/crazyblock_play/index.js": function() {
        return require("src/scripts/crazyblock_play/index.js");
    },
    "src/scripts/cupclear_level/index.js": function() {
        return require("src/scripts/cupclear_level/index.js");
    },
    "src/scripts/cupclear_play/index.js": function() {
        return require("src/scripts/cupclear_play/index.js");
    },
    "src/scripts/daily_roll/index.js": function() {
        return require("src/scripts/daily_roll/index.js");
    },
    "src/scripts/daily_roll_show/index.js": function() {
        return require("src/scripts/daily_roll_show/index.js");
    },
    "src/scripts/friend_info_v161/index.js": function() {
        return require("src/scripts/friend_info_v161/index.js");
    },
    "src/scripts/friend_invite_v161/index.js": function() {
        return require("src/scripts/friend_invite_v161/index.js");
    },
    "src/scripts/friend_main_v161/index.js": function() {
        return require("src/scripts/friend_main_v161/index.js");
    },
    "src/scripts/friend_note_v161/index.js": function() {
        return require("src/scripts/friend_note_v161/index.js");
    },
    "src/scripts/friend_ok_v161/index.js": function() {
        return require("src/scripts/friend_ok_v161/index.js");
    },
    "src/scripts/gift_collectgame/index.js": function() {
        return require("src/scripts/gift_collectgame/index.js");
    },
    "src/scripts/gift_desktop/index.js": function() {
        return require("src/scripts/gift_desktop/index.js");
    },
    "src/scripts/gift_diamond_wantall/index.js": function() {
        return require("src/scripts/gift_diamond_wantall/index.js");
    },
    "src/scripts/gift_endless_score/index.js": function() {
        return require("src/scripts/gift_endless_score/index.js");
    },
    "src/scripts/gift_endless_super/index.js": function() {
        return require("src/scripts/gift_endless_super/index.js");
    },
    "src/scripts/gift_level/index.js": function() {
        return require("src/scripts/gift_level/index.js");
    },
    "src/scripts/guide_bubble/index.js": function() {
        return require("src/scripts/guide_bubble/index.js");
    },
    "src/scripts/guide_unlock/index.js": function() {
        return require("src/scripts/guide_unlock/index.js");
    },
    "src/scripts/hardorder_chest/index.js": function() {
        return require("src/scripts/hardorder_chest/index.js");
    },
    "src/scripts/hardorder_open/index.js": function() {
        return require("src/scripts/hardorder_open/index.js");
    },
    "src/scripts/passport_level_open/index.js": function() {
        return require("src/scripts/passport_level_open/index.js");
    },
    "src/scripts/passport_level_over/index.js": function() {
        return require("src/scripts/passport_level_over/index.js");
    },
    "src/scripts/passport_level_view/index.js": function() {
        return require("src/scripts/passport_level_view/index.js");
    },
    "src/scripts/passport_season_over/index.js": function() {
        return require("src/scripts/passport_season_over/index.js");
    },
    "src/scripts/passport_cupid_finish/index.js": function() {
        return require("src/scripts/passport_cupid_finish/index.js");
    },
    "src/scripts/passport_cupid_main/index.js": function() {
        return require("src/scripts/passport_cupid_main/index.js");
    },
    "src/scripts/passport_cupid_pay/index.js": function() {
        return require("src/scripts/passport_cupid_pay/index.js");
    },
    "src/scripts/passport_cupid_pay_info/index.js": function() {
        return require("src/scripts/passport_cupid_pay_info/index.js");
    },
    "src/scripts/passport_cupid_taskover/index.js": function() {
        return require("src/scripts/passport_cupid_taskover/index.js");
    },
    "src/scripts/passport_kda_finish/index.js": function() {
        return require("src/scripts/passport_kda_finish/index.js");
    },
    "src/scripts/passport_kda_main/index.js": function() {
        return require("src/scripts/passport_kda_main/index.js");
    },
    "src/scripts/passport_kda_pay/index.js": function() {
        return require("src/scripts/passport_kda_pay/index.js");
    },
    "src/scripts/passport_kda_pay_info/index.js": function() {
        return require("src/scripts/passport_kda_pay_info/index.js");
    },
    "src/scripts/passport_kda_taskover/index.js": function() {
        return require("src/scripts/passport_kda_taskover/index.js");
    },
    "src/scripts/passport_third_finish/index.js": function() {
        return require("src/scripts/passport_third_finish/index.js");
    },
    "src/scripts/passport_third_main/index.js": function() {
        return require("src/scripts/passport_third_main/index.js");
    },
    "src/scripts/passport_third_pay/index.js": function() {
        return require("src/scripts/passport_third_pay/index.js");
    },
    "src/scripts/passport_third_pay_info/index.js": function() {
        return require("src/scripts/passport_third_pay_info/index.js");
    },
    "src/scripts/passport_third_taskover/index.js": function() {
        return require("src/scripts/passport_third_taskover/index.js");
    },
    "src/scripts/beach_paygift/index.js": function() {
        return require("src/scripts/beach_paygift/index.js");
    },
    "src/scripts/garden_paygift/index.js": function() {
        return require("src/scripts/garden_paygift/index.js");
    },
    "src/scripts/paygift_3pack/index.js": function() {
        return require("src/scripts/paygift_3pack/index.js");
    },
    "src/scripts/paygift_actcake_v161/index.js": function() {
        return require("src/scripts/paygift_actcake_v161/index.js");
    },
    "src/scripts/paygift_bargain/index.js": function() {
        return require("src/scripts/paygift_bargain/index.js");
    },
    "src/scripts/paygift_blindbox/index.js": function() {
        return require("src/scripts/paygift_blindbox/index.js");
    },
    "src/scripts/paygift_candy/index.js": function() {
        return require("src/scripts/paygift_candy/index.js");
    },
    "src/scripts/paygift_catgarden/index.js": function() {
        return require("src/scripts/paygift_catgarden/index.js");
    },
    "src/scripts/paygift_childrenday/index.js": function() {
        return require("src/scripts/paygift_childrenday/index.js");
    },
    "src/scripts/paygift_coffee/index.js": function() {
        return require("src/scripts/paygift_coffee/index.js");
    },
    "src/scripts/paygift_diamondminer_v161/index.js": function() {
        return require("src/scripts/paygift_diamondminer_v161/index.js");
    },
    "src/scripts/paygift_dig/index.js": function() {
        return require("src/scripts/paygift_dig/index.js");
    },
    "src/scripts/paygift_disco/index.js": function() {
        return require("src/scripts/paygift_disco/index.js");
    },
    "src/scripts/paygift_eaves/index.js": function() {
        return require("src/scripts/paygift_eaves/index.js");
    },
    "src/scripts/paygift_endless/index.js": function() {
        return require("src/scripts/paygift_endless/index.js");
    },
    "src/scripts/paygift_enjoysilk_v161/index.js": function() {
        return require("src/scripts/paygift_enjoysilk_v161/index.js");
    },
    "src/scripts/paygift_fan/index.js": function() {
        return require("src/scripts/paygift_fan/index.js");
    },
    "src/scripts/paygift_ferriswheel/index.js": function() {
        return require("src/scripts/paygift_ferriswheel/index.js");
    },
    "src/scripts/paygift_flower/index.js": function() {
        return require("src/scripts/paygift_flower/index.js");
    },
    "src/scripts/paygift_fragrance/index.js": function() {
        return require("src/scripts/paygift_fragrance/index.js");
    },
    "src/scripts/paygift_goddess/index.js": function() {
        return require("src/scripts/paygift_goddess/index.js");
    },
    "src/scripts/paygift_honey/index.js": function() {
        return require("src/scripts/paygift_honey/index.js");
    },
    "src/scripts/paygift_lantern/index.js": function() {
        return require("src/scripts/paygift_lantern/index.js");
    },
    "src/scripts/paygift_lanternhigh_v161/index.js": function() {
        return require("src/scripts/paygift_lanternhigh_v161/index.js");
    },
    "src/scripts/paygift_newerwantall/index.js": function() {
        return require("src/scripts/paygift_newerwantall/index.js");
    },
    "src/scripts/paygift_pege/index.js": function() {
        return require("src/scripts/paygift_pege/index.js");
    },
    "src/scripts/paygift_progressive/index.js": function() {
        return require("src/scripts/paygift_progressive/index.js");
    },
    "src/scripts/paygift_queen/index.js": function() {
        return require("src/scripts/paygift_queen/index.js");
    },
    "src/scripts/paygift_recharge/index.js": function() {
        return require("src/scripts/paygift_recharge/index.js");
    },
    "src/scripts/paygift_s10huafei/index.js": function() {
        return require("src/scripts/paygift_s10huafei/index.js");
    },
    "src/scripts/paygift_s10qianying/index.js": function() {
        return require("src/scripts/paygift_s10qianying/index.js");
    },
    "src/scripts/paygift_s10wuxie/index.js": function() {
        return require("src/scripts/paygift_s10wuxie/index.js");
    },
    "src/scripts/paygift_s11cold/index.js": function() {
        return require("src/scripts/paygift_s11cold/index.js");
    },
    "src/scripts/paygift_s11friend/index.js": function() {
        return require("src/scripts/paygift_s11friend/index.js");
    },
    "src/scripts/paygift_s11warmsun/index.js": function() {
        return require("src/scripts/paygift_s11warmsun/index.js");
    },
    "src/scripts/paygift_s12snownight/index.js": function() {
        return require("src/scripts/paygift_s12snownight/index.js");
    },
    "src/scripts/paygift_s12staregg/index.js": function() {
        return require("src/scripts/paygift_s12staregg/index.js");
    },
    "src/scripts/paygift_s12xiehou/index.js": function() {
        return require("src/scripts/paygift_s12xiehou/index.js");
    },
    "src/scripts/paygift_s13money/index.js": function() {
        return require("src/scripts/paygift_s13money/index.js");
    },
    "src/scripts/paygift_s13snake/index.js": function() {
        return require("src/scripts/paygift_s13snake/index.js");
    },
    "src/scripts/paygift_s13wufu/index.js": function() {
        return require("src/scripts/paygift_s13wufu/index.js");
    },
    "src/scripts/paygift_s3fool/index.js": function() {
        return require("src/scripts/paygift_s3fool/index.js");
    },
    "src/scripts/paygift_s3green/index.js": function() {
        return require("src/scripts/paygift_s3green/index.js");
    },
    "src/scripts/paygift_s3sunflower/index.js": function() {
        return require("src/scripts/paygift_s3sunflower/index.js");
    },
    "src/scripts/paygift_s3water/index.js": function() {
        return require("src/scripts/paygift_s3water/index.js");
    },
    "src/scripts/paygift_s4bike/index.js": function() {
        return require("src/scripts/paygift_s4bike/index.js");
    },
    "src/scripts/paygift_s4boxing/index.js": function() {
        return require("src/scripts/paygift_s4boxing/index.js");
    },
    "src/scripts/paygift_s4camp/index.js": function() {
        return require("src/scripts/paygift_s4camp/index.js");
    },
    "src/scripts/paygift_s4coaster/index.js": function() {
        return require("src/scripts/paygift_s4coaster/index.js");
    },
    "src/scripts/paygift_s4food/index.js": function() {
        return require("src/scripts/paygift_s4food/index.js");
    },
    "src/scripts/paygift_s4ootd/index.js": function() {
        return require("src/scripts/paygift_s4ootd/index.js");
    },
    "src/scripts/paygift_s4school/index.js": function() {
        return require("src/scripts/paygift_s4school/index.js");
    },
    "src/scripts/paygift_s5birthday/index.js": function() {
        return require("src/scripts/paygift_s5birthday/index.js");
    },
    "src/scripts/paygift_s5deer/index.js": function() {
        return require("src/scripts/paygift_s5deer/index.js");
    },
    "src/scripts/paygift_s5jasmine/index.js": function() {
        return require("src/scripts/paygift_s5jasmine/index.js");
    },
    "src/scripts/paygift_s5justyou/index.js": function() {
        return require("src/scripts/paygift_s5justyou/index.js");
    },
    "src/scripts/paygift_s5mom/index.js": function() {
        return require("src/scripts/paygift_s5mom/index.js");
    },
    "src/scripts/paygift_s5playground/index.js": function() {
        return require("src/scripts/paygift_s5playground/index.js");
    },
    "src/scripts/paygift_s5roses/index.js": function() {
        return require("src/scripts/paygift_s5roses/index.js");
    },
    "src/scripts/paygift_s5sailing/index.js": function() {
        return require("src/scripts/paygift_s5sailing/index.js");
    },
    "src/scripts/paygift_s6around/index.js": function() {
        return require("src/scripts/paygift_s6around/index.js");
    },
    "src/scripts/paygift_s6begonia/index.js": function() {
        return require("src/scripts/paygift_s6begonia/index.js");
    },
    "src/scripts/paygift_s6duanwu/index.js": function() {
        return require("src/scripts/paygift_s6duanwu/index.js");
    },
    "src/scripts/paygift_s6fireworks/index.js": function() {
        return require("src/scripts/paygift_s6fireworks/index.js");
    },
    "src/scripts/paygift_s6panda/index.js": function() {
        return require("src/scripts/paygift_s6panda/index.js");
    },
    "src/scripts/paygift_s7flower/index.js": function() {
        return require("src/scripts/paygift_s7flower/index.js");
    },
    "src/scripts/paygift_s7hydrangeas/index.js": function() {
        return require("src/scripts/paygift_s7hydrangeas/index.js");
    },
    "src/scripts/paygift_s7pearls/index.js": function() {
        return require("src/scripts/paygift_s7pearls/index.js");
    },
    "src/scripts/paygift_s7qixi/index.js": function() {
        return require("src/scripts/paygift_s7qixi/index.js");
    },
    "src/scripts/paygift_s7sea/index.js": function() {
        return require("src/scripts/paygift_s7sea/index.js");
    },
    "src/scripts/paygift_s7summer/index.js": function() {
        return require("src/scripts/paygift_s7summer/index.js");
    },
    "src/scripts/paygift_s7watermelon/index.js": function() {
        return require("src/scripts/paygift_s7watermelon/index.js");
    },
    "src/scripts/paygift_s8operationkey/index.js": function() {
        return require("src/scripts/paygift_s8operationkey/index.js");
    },
    "src/scripts/paygift_s9coffee/index.js": function() {
        return require("src/scripts/paygift_s9coffee/index.js");
    },
    "src/scripts/paygift_s9jianjia/index.js": function() {
        return require("src/scripts/paygift_s9jianjia/index.js");
    },
    "src/scripts/paygift_s9juhua/index.js": function() {
        return require("src/scripts/paygift_s9juhua/index.js");
    },
    "src/scripts/paygift_s9magickey/index.js": function() {
        return require("src/scripts/paygift_s9magickey/index.js");
    },
    "src/scripts/paygift_savenewcard/index.js": function() {
        return require("src/scripts/paygift_savenewcard/index.js");
    },
    "src/scripts/paygift_skykoi_v161/index.js": function() {
        return require("src/scripts/paygift_skykoi_v161/index.js");
    },
    "src/scripts/paygift_snowman_v161/index.js": function() {
        return require("src/scripts/paygift_snowman_v161/index.js");
    },
    "src/scripts/paygift_springletter_v161/index.js": function() {
        return require("src/scripts/paygift_springletter_v161/index.js");
    },
    "src/scripts/paygift_surenew1_v161/index.js": function() {
        return require("src/scripts/paygift_surenew1_v161/index.js");
    },
    "src/scripts/paygift_wantall/index.js": function() {
        return require("src/scripts/paygift_wantall/index.js");
    },
    "src/scripts/paygift_warmlight/index.js": function() {
        return require("src/scripts/paygift_warmlight/index.js");
    },
    "src/scripts/paygift_wildcard/index.js": function() {
        return require("src/scripts/paygift_wildcard/index.js");
    },
    "src/scripts/paygift_wintermoon_v161/index.js": function() {
        return require("src/scripts/paygift_wintermoon_v161/index.js");
    },
    "src/scripts/player_badge_reward/index.js": function() {
        return require("src/scripts/player_badge_reward/index.js");
    },
    "src/scripts/pushball_level/index.js": function() {
        return require("src/scripts/pushball_level/index.js");
    },
    "src/scripts/pushball_play/index.js": function() {
        return require("src/scripts/pushball_play/index.js");
    },
    "src/scripts/restroom_level/index.js": function() {
        return require("src/scripts/restroom_level/index.js");
    },
    "src/scripts/restroom_play/index.js": function() {
        return require("src/scripts/restroom_play/index.js");
    },
    "src/scripts/return_gift_letter/index.js": function() {
        return require("src/scripts/return_gift_letter/index.js");
    },
    "src/scripts/return_gift_main/index.js": function() {
        return require("src/scripts/return_gift_main/index.js");
    },
    "src/scripts/royal_leave/index.js": function() {
        return require("src/scripts/royal_leave/index.js");
    },
    "src/scripts/royal_level/index.js": function() {
        return require("src/scripts/royal_level/index.js");
    },
    "src/scripts/royal_play/index.js": function() {
        return require("src/scripts/royal_play/index.js");
    },
    "src/scripts/royal_result/index.js": function() {
        return require("src/scripts/royal_result/index.js");
    },
    "src/scripts/russia_level/index.js": function() {
        return require("src/scripts/russia_level/index.js");
    },
    "src/scripts/savegirl_leave/index.js": function() {
        return require("src/scripts/savegirl_leave/index.js");
    },
    "src/scripts/savegirl_level/index.js": function() {
        return require("src/scripts/savegirl_level/index.js");
    },
    "src/scripts/savegirl_play/index.js": function() {
        return require("src/scripts/savegirl_play/index.js");
    },
    "src/scripts/savegirl_result/index.js": function() {
        return require("src/scripts/savegirl_result/index.js");
    },
    "src/scripts/secret_protocol/index.js": function() {
        return require("src/scripts/secret_protocol/index.js");
    },
    "src/scripts/day_sign/index.js": function() {
        return require("src/scripts/day_sign/index.js");
    },
    "src/scripts/return_sign/index.js": function() {
        return require("src/scripts/return_sign/index.js");
    },
    "src/scripts/storymap_map/index.js": function() {
        return require("src/scripts/storymap_map/index.js");
    },
    "src/scripts/storymap_open/index.js": function() {
        return require("src/scripts/storymap_open/index.js");
    },
    "src/scripts/storymap_tip/index.js": function() {
        return require("src/scripts/storymap_tip/index.js");
    },
    "src/scripts/wildcard_exchange/index.js": function() {
        return require("src/scripts/wildcard_exchange/index.js");
    },
    "src/scripts/wildcard_exchange_got/index.js": function() {
        return require("src/scripts/wildcard_exchange_got/index.js");
    },
    "src/scripts/wildcard_exchange_tip/index.js": function() {
        return require("src/scripts/wildcard_exchange_tip/index.js");
    },
    "src/scripts/wildcard_reward/index.js": function() {
        return require("src/scripts/wildcard_reward/index.js");
    },
    "src/scripts/newyear_reward_v161/index.js": function() {
        return require("src/scripts/newyear_reward_v161/index.js");
    },
    "src/scripts/penguin_again/index.js": function() {
        return require("src/scripts/penguin_again/index.js");
    },
    "src/scripts/penguin_confirm_v161/index.js": function() {
        return require("src/scripts/penguin_confirm_v161/index.js");
    },
    "src/scripts/penguin_info_v161/index.js": function() {
        return require("src/scripts/penguin_info_v161/index.js");
    },
    "src/scripts/penguin_join_v161/index.js": function() {
        return require("src/scripts/penguin_join_v161/index.js");
    },
    "src/scripts/penguin_main_v161/index.js": function() {
        return require("src/scripts/penguin_main_v161/index.js");
    },
    "src/scripts/penguin_point/index.js": function() {
        return require("src/scripts/penguin_point/index.js");
    },
    "src/scripts/penguin_revive_v161/index.js": function() {
        return require("src/scripts/penguin_revive_v161/index.js");
    },
    "src/scripts/penguin_reward_v161/index.js": function() {
        return require("src/scripts/penguin_reward_v161/index.js");
    },
    "src/scripts/penguin_start_v161/index.js": function() {
        return require("src/scripts/penguin_start_v161/index.js");
    },
    "src/scripts/pet_main/index.js": function() {
        return require("src/scripts/pet_main/index.js");
    },
    "src/scripts/pet_reward/index.js": function() {
        return require("src/scripts/pet_reward/index.js");
    },
    "src/scripts/retro_library_main/index.js": function() {
        return require("src/scripts/retro_library_main/index.js");
    },
    "src/scripts/retro_library_reward/index.js": function() {
        return require("src/scripts/retro_library_reward/index.js");
    },
    "src/scripts/snowman_main_v161/index.js": function() {
        return require("src/scripts/snowman_main_v161/index.js");
    },
    "src/scripts/snowman_reward_v161/index.js": function() {
        return require("src/scripts/snowman_reward_v161/index.js");
    },
    "src/scripts/spring_main/index.js": function() {
        return require("src/scripts/spring_main/index.js");
    },
    "src/scripts/spring_reward/index.js": function() {
        return require("src/scripts/spring_reward/index.js");
    },
    "src/scripts/stream_rest_main/index.js": function() {
        return require("src/scripts/stream_rest_main/index.js");
    },
    "src/scripts/stream_rest_reward/index.js": function() {
        return require("src/scripts/stream_rest_reward/index.js");
    },
    "src/scripts/stream_rest_start/index.js": function() {
        return require("src/scripts/stream_rest_start/index.js");
    },
    "src/scripts/summer_night_main/index.js": function() {
        return require("src/scripts/summer_night_main/index.js");
    },
    "src/scripts/summer_night_reward/index.js": function() {
        return require("src/scripts/summer_night_reward/index.js");
    },
    "src/scripts/sweetheart_main/index.js": function() {
        return require("src/scripts/sweetheart_main/index.js");
    },
    "src/scripts/sweetheart_reward/index.js": function() {
        return require("src/scripts/sweetheart_reward/index.js");
    },
    "src/scripts/volcano_again_v161/index.js": function() {
        return require("src/scripts/volcano_again_v161/index.js");
    },
    "src/scripts/volcano_confirm_v161/index.js": function() {
        return require("src/scripts/volcano_confirm_v161/index.js");
    },
    "src/scripts/volcano_info_v161/index.js": function() {
        return require("src/scripts/volcano_info_v161/index.js");
    },
    "src/scripts/volcano_join_v161/index.js": function() {
        return require("src/scripts/volcano_join_v161/index.js");
    },
    "src/scripts/volcano_main_v161/index.js": function() {
        return require("src/scripts/volcano_main_v161/index.js");
    },
    "src/scripts/volcano_point/index.js": function() {
        return require("src/scripts/volcano_point/index.js");
    },
    "src/scripts/volcano_revive_v161/index.js": function() {
        return require("src/scripts/volcano_revive_v161/index.js");
    },
    "src/scripts/volcano_reward_v161/index.js": function() {
        return require("src/scripts/volcano_reward_v161/index.js");
    },
    "src/scripts/volcano_start_v161/index.js": function() {
        return require("src/scripts/volcano_start_v161/index.js");
    },
    "src/scripts/cardsrecord_record/index.js": function() {
        return require("src/scripts/cardsrecord_record/index.js");
    },
    "src/scripts/airship_confirm/index.js": function() {
        return require("src/scripts/airship_confirm/index.js");
    },
    "src/scripts/airship_fail/index.js": function() {
        return require("src/scripts/airship_fail/index.js");
    },
    "src/scripts/airship_info/index.js": function() {
        return require("src/scripts/airship_info/index.js");
    },
    "src/scripts/airship_main/index.js": function() {
        return require("src/scripts/airship_main/index.js");
    },
    "src/scripts/airship_revive/index.js": function() {
        return require("src/scripts/airship_revive/index.js");
    },
    "src/scripts/airship_reward/index.js": function() {
        return require("src/scripts/airship_reward/index.js");
    },
    "src/scripts/airship_start/index.js": function() {
        return require("src/scripts/airship_start/index.js");
    },
    "src/scripts/detective_info/index.js": function() {
        return require("src/scripts/detective_info/index.js");
    },
    "src/scripts/detective_main/index.js": function() {
        return require("src/scripts/detective_main/index.js");
    },
    "src/scripts/detective_pay_info/index.js": function() {
        return require("src/scripts/detective_pay_info/index.js");
    },
    "src/scripts/detective_pay_view/index.js": function() {
        return require("src/scripts/detective_pay_view/index.js");
    },
    "src/scripts/detective_paygift/index.js": function() {
        return require("src/scripts/detective_paygift/index.js");
    },
    "src/scripts/detective_reward/index.js": function() {
        return require("src/scripts/detective_reward/index.js");
    },
    "src/scripts/detective_start/index.js": function() {
        return require("src/scripts/detective_start/index.js");
    },
    "src/scripts/helicopter_info/index.js": function() {
        return require("src/scripts/helicopter_info/index.js");
    },
    "src/scripts/helicopter_main/index.js": function() {
        return require("src/scripts/helicopter_main/index.js");
    },
    "src/scripts/helicopter_reward/index.js": function() {
        return require("src/scripts/helicopter_reward/index.js");
    },
    "src/scripts/helicopter_round/index.js": function() {
        return require("src/scripts/helicopter_round/index.js");
    },
    "src/scripts/helicopter_start/index.js": function() {
        return require("src/scripts/helicopter_start/index.js");
    },
    "src/scripts/sabc_round/index.js": function() {
        return require("src/scripts/sabc_round/index.js");
    },
    "src/scripts/sabc_start/index.js": function() {
        return require("src/scripts/sabc_start/index.js");
    },
    "src/scripts/secondboard_info/index.js": function() {
        return require("src/scripts/secondboard_info/index.js");
    },
    "src/scripts/secondboard_main/index.js": function() {
        return require("src/scripts/secondboard_main/index.js");
    },
    "src/scripts/secondboard_newchess/index.js": function() {
        return require("src/scripts/secondboard_newchess/index.js");
    },
    "src/scripts/secondboard_paygift/index.js": function() {
        return require("src/scripts/secondboard_paygift/index.js");
    },
    "src/scripts/secondboard_rank/index.js": function() {
        return require("src/scripts/secondboard_rank/index.js");
    },
    "src/scripts/secondboard_rank_reward/index.js": function() {
        return require("src/scripts/secondboard_rank_reward/index.js");
    },
    "src/scripts/secondboard_reward/index.js": function() {
        return require("src/scripts/secondboard_reward/index.js");
    },
    "src/scripts/sheep_confirm/index.js": function() {
        return require("src/scripts/sheep_confirm/index.js");
    },
    "src/scripts/sheep_energy/index.js": function() {
        return require("src/scripts/sheep_energy/index.js");
    },
    "src/scripts/sheep_fail/index.js": function() {
        return require("src/scripts/sheep_fail/index.js");
    },
    "src/scripts/sheep_finish/index.js": function() {
        return require("src/scripts/sheep_finish/index.js");
    },
    "src/scripts/sheep_game_info/index.js": function() {
        return require("src/scripts/sheep_game_info/index.js");
    },
    "src/scripts/sheep_main/index.js": function() {
        return require("src/scripts/sheep_main/index.js");
    },
    "src/scripts/sheep_main_info/index.js": function() {
        return require("src/scripts/sheep_main_info/index.js");
    },
    "src/scripts/sheep_over/index.js": function() {
        return require("src/scripts/sheep_over/index.js");
    },
    "src/scripts/sheep_paygift/index.js": function() {
        return require("src/scripts/sheep_paygift/index.js");
    },
    "src/scripts/sheep_props/index.js": function() {
        return require("src/scripts/sheep_props/index.js");
    },
    "src/scripts/sheep_rank/index.js": function() {
        return require("src/scripts/sheep_rank/index.js");
    },
    "src/scripts/sheep_reward/index.js": function() {
        return require("src/scripts/sheep_reward/index.js");
    },
    "src/scripts/sheep_start/index.js": function() {
        return require("src/scripts/sheep_start/index.js");
    },
    "src/scripts/sheep_win/index.js": function() {
        return require("src/scripts/sheep_win/index.js");
    },
    "src/scripts/super_egg_gacha/index.js": function() {
        return require("src/scripts/super_egg_gacha/index.js");
    },
    "src/scripts/super_egg_info/index.js": function() {
        return require("src/scripts/super_egg_info/index.js");
    },
    "src/scripts/super_egg_list/index.js": function() {
        return require("src/scripts/super_egg_list/index.js");
    },
    "src/scripts/super_egg_main/index.js": function() {
        return require("src/scripts/super_egg_main/index.js");
    },
    "src/scripts/super_egg_preview/index.js": function() {
        return require("src/scripts/super_egg_preview/index.js");
    },
    "src/scripts/super_egg_reward/index.js": function() {
        return require("src/scripts/super_egg_reward/index.js");
    },
    "src/scripts/super_egg_tip/index.js": function() {
        return require("src/scripts/super_egg_tip/index.js");
    },
    "src/scripts/treasure_box_confirm/index.js": function() {
        return require("src/scripts/treasure_box_confirm/index.js");
    },
    "src/scripts/treasure_box_fail/index.js": function() {
        return require("src/scripts/treasure_box_fail/index.js");
    },
    "src/scripts/treasure_box_info/index.js": function() {
        return require("src/scripts/treasure_box_info/index.js");
    },
    "src/scripts/treasure_box_main/index.js": function() {
        return require("src/scripts/treasure_box_main/index.js");
    },
    "src/scripts/treasure_box_paygift/index.js": function() {
        return require("src/scripts/treasure_box_paygift/index.js");
    },
    "src/scripts/treasure_box_reward/index.js": function() {
        return require("src/scripts/treasure_box_reward/index.js");
    },
    "src/scripts/treasure_box_start/index.js": function() {
        return require("src/scripts/treasure_box_start/index.js");
    },
    "src/scripts/treasure_hunt_change/index.js": function() {
        return require("src/scripts/treasure_hunt_change/index.js");
    },
    "src/scripts/treasure_hunt_info/index.js": function() {
        return require("src/scripts/treasure_hunt_info/index.js");
    },
    "src/scripts/treasure_hunt_main/index.js": function() {
        return require("src/scripts/treasure_hunt_main/index.js");
    },
    "src/scripts/treasure_hunt_paygift/index.js": function() {
        return require("src/scripts/treasure_hunt_paygift/index.js");
    },
    "src/scripts/treasure_hunt_reward/index.js": function() {
        return require("src/scripts/treasure_hunt_reward/index.js");
    },
    "src/scripts/treasure_magic_challenge/index.js": function() {
        return require("src/scripts/treasure_magic_challenge/index.js");
    },
    "src/scripts/treasure_magic_confirm/index.js": function() {
        return require("src/scripts/treasure_magic_confirm/index.js");
    },
    "src/scripts/treasure_magic_fail/index.js": function() {
        return require("src/scripts/treasure_magic_fail/index.js");
    },
    "src/scripts/treasure_magic_fail_confirm/index.js": function() {
        return require("src/scripts/treasure_magic_fail_confirm/index.js");
    },
    "src/scripts/treasure_magic_info/index.js": function() {
        return require("src/scripts/treasure_magic_info/index.js");
    },
    "src/scripts/treasure_magic_main_a/index.js": function() {
        return require("src/scripts/treasure_magic_main_a/index.js");
    },
    "src/scripts/treasure_magic_paygift/index.js": function() {
        return require("src/scripts/treasure_magic_paygift/index.js");
    },
    "src/scripts/treasure_magic_reward/index.js": function() {
        return require("src/scripts/treasure_magic_reward/index.js");
    },
    "src/scripts/treasure_magic_start/index.js": function() {
        return require("src/scripts/treasure_magic_start/index.js");
    },
    "src/scripts/cake_info_v161/index.js": function() {
        return require("src/scripts/cake_info_v161/index.js");
    },
    "src/scripts/cake_invite_v161/index.js": function() {
        return require("src/scripts/cake_invite_v161/index.js");
    },
    "src/scripts/cake_lottery_v161/index.js": function() {
        return require("src/scripts/cake_lottery_v161/index.js");
    },
    "src/scripts/cake_main_v161/index.js": function() {
        return require("src/scripts/cake_main_v161/index.js");
    },
    "src/scripts/cake_start_v161/index.js": function() {
        return require("src/scripts/cake_start_v161/index.js");
    },
    "src/scripts/partner_reward_info/index.js": function() {
        return require("src/scripts/partner_reward_info/index.js");
    },
    "src/scripts/beach_chess/index.js": function() {
        return require("src/scripts/beach_chess/index.js");
    },
    "src/scripts/beach_info/index.js": function() {
        return require("src/scripts/beach_info/index.js");
    },
    "src/scripts/beach_points/index.js": function() {
        return require("src/scripts/beach_points/index.js");
    },
    "src/scripts/beach_reward/index.js": function() {
        return require("src/scripts/beach_reward/index.js");
    },
    "src/scripts/garden_chess/index.js": function() {
        return require("src/scripts/garden_chess/index.js");
    },
    "src/scripts/garden_info/index.js": function() {
        return require("src/scripts/garden_info/index.js");
    },
    "src/scripts/garden_main/index.js": function() {
        return require("src/scripts/garden_main/index.js");
    },
    "src/scripts/garden_points/index.js": function() {
        return require("src/scripts/garden_points/index.js");
    },
    "src/scripts/garden_reward/index.js": function() {
        return require("src/scripts/garden_reward/index.js");
    },
    "src/scripts/sweet101_info/index.js": function() {
        return require("src/scripts/sweet101_info/index.js");
    },
    "src/scripts/sweet101_main/index.js": function() {
        return require("src/scripts/sweet101_main/index.js");
    },
    "src/scripts/sweet101_reward/index.js": function() {
        return require("src/scripts/sweet101_reward/index.js");
    },
    "src/scripts/sweet101_unlock/index.js": function() {
        return require("src/scripts/sweet101_unlock/index.js");
    },
    "src/scripts/coconut_info/index.js": function() {
        return require("src/scripts/coconut_info/index.js");
    },
    "src/scripts/coconut_main/index.js": function() {
        return require("src/scripts/coconut_main/index.js");
    },
    "src/scripts/coconut_reward/index.js": function() {
        return require("src/scripts/coconut_reward/index.js");
    },
    "src/scripts/coconut_unlock/index.js": function() {
        return require("src/scripts/coconut_unlock/index.js");
    },
    "src/scripts/dingdang_fish_info/index.js": function() {
        return require("src/scripts/dingdang_fish_info/index.js");
    },
    "src/scripts/dingdang_fish_main/index.js": function() {
        return require("src/scripts/dingdang_fish_main/index.js");
    },
    "src/scripts/dingdang_fish_reward/index.js": function() {
        return require("src/scripts/dingdang_fish_reward/index.js");
    },
    "src/scripts/dingdang_fish_unlock/index.js": function() {
        return require("src/scripts/dingdang_fish_unlock/index.js");
    },
    "src/scripts/dingdang_yoga_info/index.js": function() {
        return require("src/scripts/dingdang_yoga_info/index.js");
    },
    "src/scripts/dingdang_yoga_main/index.js": function() {
        return require("src/scripts/dingdang_yoga_main/index.js");
    },
    "src/scripts/dingdang_yoga_reward/index.js": function() {
        return require("src/scripts/dingdang_yoga_reward/index.js");
    },
    "src/scripts/dingdang_yoga_unlock/index.js": function() {
        return require("src/scripts/dingdang_yoga_unlock/index.js");
    },
    "src/scripts/lucky_gewen_info/index.js": function() {
        return require("src/scripts/lucky_gewen_info/index.js");
    },
    "src/scripts/lucky_gewen_main/index.js": function() {
        return require("src/scripts/lucky_gewen_main/index.js");
    },
    "src/scripts/lucky_gewen_reward/index.js": function() {
        return require("src/scripts/lucky_gewen_reward/index.js");
    },
    "src/scripts/lucky_gewen_unlock/index.js": function() {
        return require("src/scripts/lucky_gewen_unlock/index.js");
    },
    "src/scripts/robot_pengpeng_info/index.js": function() {
        return require("src/scripts/robot_pengpeng_info/index.js");
    },
    "src/scripts/robot_pengpeng_main/index.js": function() {
        return require("src/scripts/robot_pengpeng_main/index.js");
    },
    "src/scripts/robot_pengpeng_reward/index.js": function() {
        return require("src/scripts/robot_pengpeng_reward/index.js");
    },
    "src/scripts/robot_pengpeng_unlock/index.js": function() {
        return require("src/scripts/robot_pengpeng_unlock/index.js");
    },
    "src/scripts/robot_work_info/index.js": function() {
        return require("src/scripts/robot_work_info/index.js");
    },
    "src/scripts/robot_work_main/index.js": function() {
        return require("src/scripts/robot_work_main/index.js");
    },
    "src/scripts/robot_work_reward/index.js": function() {
        return require("src/scripts/robot_work_reward/index.js");
    },
    "src/scripts/robot_work_unlock/index.js": function() {
        return require("src/scripts/robot_work_unlock/index.js");
    },
    "src/scripts/sport_main/index.js": function() {
        return require("src/scripts/sport_main/index.js");
    },
    "src/scripts/sport_reward/index.js": function() {
        return require("src/scripts/sport_reward/index.js");
    },
    "src/scripts/sport_unlock/index.js": function() {
        return require("src/scripts/sport_unlock/index.js");
    },
    "src/scripts/spring_picnic_info/index.js": function() {
        return require("src/scripts/spring_picnic_info/index.js");
    },
    "src/scripts/spring_picnic_main/index.js": function() {
        return require("src/scripts/spring_picnic_main/index.js");
    },
    "src/scripts/spring_picnic_reward/index.js": function() {
        return require("src/scripts/spring_picnic_reward/index.js");
    },
    "src/scripts/spring_picnic_unlock/index.js": function() {
        return require("src/scripts/spring_picnic_unlock/index.js");
    },
    "src/scripts/sweetspring_main/index.js": function() {
        return require("src/scripts/sweetspring_main/index.js");
    },
    "src/scripts/sweetspring_reward/index.js": function() {
        return require("src/scripts/sweetspring_reward/index.js");
    },
    "src/scripts/sweetspring_unlock/index.js": function() {
        return require("src/scripts/sweetspring_unlock/index.js");
    },
    "src/scripts/weekend_gewen_info/index.js": function() {
        return require("src/scripts/weekend_gewen_info/index.js");
    },
    "src/scripts/weekend_gewen_main/index.js": function() {
        return require("src/scripts/weekend_gewen_main/index.js");
    },
    "src/scripts/weekend_gewen_reward/index.js": function() {
        return require("src/scripts/weekend_gewen_reward/index.js");
    },
    "src/scripts/weekend_gewen_unlock/index.js": function() {
        return require("src/scripts/weekend_gewen_unlock/index.js");
    },
    "src/scripts/pege_info/index.js": function() {
        return require("src/scripts/pege_info/index.js");
    },
    "src/scripts/pege_main/index.js": function() {
        return require("src/scripts/pege_main/index.js");
    },
    "src/scripts/pege_progressive/index.js": function() {
        return require("src/scripts/pege_progressive/index.js");
    },
    "src/scripts/pege_reward/index.js": function() {
        return require("src/scripts/pege_reward/index.js");
    },
    "src/scripts/pege_sea_info/index.js": function() {
        return require("src/scripts/pege_sea_info/index.js");
    },
    "src/scripts/pege_sea_main/index.js": function() {
        return require("src/scripts/pege_sea_main/index.js");
    },
    "src/scripts/pege_sea_progressive/index.js": function() {
        return require("src/scripts/pege_sea_progressive/index.js");
    },
    "src/scripts/pege_sea_reward/index.js": function() {
        return require("src/scripts/pege_sea_reward/index.js");
    },
    "src/scripts/cards_main_dargon_v161/index.js": function() {
        return require("src/scripts/cards_main_dargon_v161/index.js");
    },
    "src/scripts/dinner_prom_main/index.js": function() {
        return require("src/scripts/dinner_prom_main/index.js");
    },
    "src/scripts/dinner_prom_info/index.js": function() {
        return require("src/scripts/dinner_prom_info/index.js");
    },
    "src/scripts/dinner_prom_pick/index.js": function() {
        return require("src/scripts/dinner_prom_pick/index.js");
    },
    "src/scripts/dinner_prom_point/index.js": function() {
        return require("src/scripts/dinner_prom_point/index.js");
    },
    "src/scripts/activity_short_tipinfo/index.js": function() {
        return require("src/scripts/activity_short_tipinfo/index.js");
    },
    "src/scripts/beach_party_main/index.js": function() {
        return require("src/scripts/beach_party_main/index.js");
    },
    "src/scripts/beach_party_reward/index.js": function() {
        return require("src/scripts/beach_party_reward/index.js");
    },
    "src/scripts/blocks_main/index.js": function() {
        return require("src/scripts/blocks_main/index.js");
    },
    "src/scripts/blocks_reward/index.js": function() {
        return require("src/scripts/blocks_reward/index.js");
    },
    "src/scripts/dinner_info/index.js": function() {
        return require("src/scripts/dinner_info/index.js");
    },
    "src/scripts/dinner_main/index.js": function() {
        return require("src/scripts/dinner_main/index.js");
    },
    "src/scripts/dinner_pick/index.js": function() {
        return require("src/scripts/dinner_pick/index.js");
    },
    "src/scripts/dinner_point/index.js": function() {
        return require("src/scripts/dinner_point/index.js");
    },
    "src/scripts/colormania_main/index.js": function() {
        return require("src/scripts/colormania_main/index.js");
    },
    "src/scripts/colormania_reward/index.js": function() {
        return require("src/scripts/colormania_reward/index.js");
    },
    "src/scripts/corgi_dog_main/index.js": function() {
        return require("src/scripts/corgi_dog_main/index.js");
    },
    "src/scripts/corgi_dog_reward/index.js": function() {
        return require("src/scripts/corgi_dog_reward/index.js");
    },
    "src/scripts/corgi_dog_start/index.js": function() {
        return require("src/scripts/corgi_dog_start/index.js");
    },
    "src/scripts/dinner_winter_info/index.js": function() {
        return require("src/scripts/dinner_winter_info/index.js");
    },
    "src/scripts/dinner_winter_main/index.js": function() {
        return require("src/scripts/dinner_winter_main/index.js");
    },
    "src/scripts/dinner_winter_pick/index.js": function() {
        return require("src/scripts/dinner_winter_pick/index.js");
    },
    "src/scripts/dinner_winter_point/index.js": function() {
        return require("src/scripts/dinner_winter_point/index.js");
    },
    "src/scripts/dumpling_main/index.js": function() {
        return require("src/scripts/dumpling_main/index.js");
    },
    "src/scripts/dumpling_reward/index.js": function() {
        return require("src/scripts/dumpling_reward/index.js");
    },
    "src/scripts/funpark_main/index.js": function() {
        return require("src/scripts/funpark_main/index.js");
    },
    "src/scripts/funpark_reward/index.js": function() {
        return require("src/scripts/funpark_reward/index.js");
    },
    "src/scripts/hydrangeas_main/index.js": function() {
        return require("src/scripts/hydrangeas_main/index.js");
    },
    "src/scripts/hydrangeas_reward/index.js": function() {
        return require("src/scripts/hydrangeas_reward/index.js");
    },
    "src/scripts/hydrangeas_start/index.js": function() {
        return require("src/scripts/hydrangeas_start/index.js");
    },
    "src/scripts/motorboat_main/index.js": function() {
        return require("src/scripts/motorboat_main/index.js");
    },
    "src/scripts/motorboat_reward/index.js": function() {
        return require("src/scripts/motorboat_reward/index.js");
    },
    "src/scripts/newyear_main_v161/index.js": function() {
        return require("src/scripts/newyear_main_v161/index.js");
    },
    "src/scripts/winter_dinner_pick/index.js": function() {
        return require("src/scripts/winter_dinner_pick/index.js");
    },
    "src/scripts/winter_dinner_point/index.js": function() {
        return require("src/scripts/winter_dinner_point/index.js");
    },
    "src/scripts/winter_dinner_info/index.js": function() {
        return require("src/scripts/winter_dinner_info/index.js");
    },
    "src/scripts/winter_dinner_main/index.js": function() {
        return require("src/scripts/winter_dinner_main/index.js");
    }
};

window.__cocos_require__ = function(s) {
    var i = r[s];
    if (!i) throw new Error("cannot find module ".concat(s));
    return i();
};