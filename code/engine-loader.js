Object.defineProperty(exports, "__esModule", {
    value: !0
}), exports.loadEngine = void 0;

exports.loadEngine = function e(n, o) {
    wx.loadSubpackage ? function(e) {
        return new Promise(function(n, o) {
            var t = new Date().getTime();
            wx.loadSubpackage({
                name: e,
                success: function(o) {
                    console.log("引擎子包加载完毕", e, new Date().getTime() - t, "ms"), n(!0);
                },
                fail: function(o) {
                    console.log("引擎子包加载失败", e, new Date().getTime() - t, "ms"), n(!1);
                }
            }).onProgressUpdate(function(e) {});
        });
    }(n).then(function(t) {
        t ? o && o() : e(n);
    }) : (require(n + "/game.js"), o && o());
};