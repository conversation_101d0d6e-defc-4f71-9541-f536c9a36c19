[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "ald.d", "declare namespace wx {\n    /**\n     * 调用接口获取**登录凭证（code）**进而换取用户登录态信息，包括用户的**唯一标识（openid）** 及本次登录的 **会话密钥（session_key）**等。**用户数据的加解密通讯**需要依赖会话密钥完成。\n     */\n    export function login(object: _LoginObject): void;\n    export function onTouchStart(fn: Function);\n    export function onTouchMove(fn: Function);\n    export function onTouchEnd(fn: Function);\n    export function onTouchCancel(fn: Function);\n    export function offTouchStart(fn: Function);\n    export function offTouchMove(fn: Function);\n    export function offTouchEnd(fn: Function);\n    export function offTouchCancel(fn: Function);\n    export function getMenuButtonBoundingClientRect();\n    export function onNetworkStatusChange(fn: (res: _NetworkStatusChange) => void);\n    export function requestMidasPaymentGameItem(object: _MidasPaymentGameItemObject);\n\n    export function closeSocket(data: SocketCloseData): void;\n    export function sendSocketMessage(data: SocketSendData): void;\n    export function connectSocket({ url: string }): SocketTask;\n    export function onSocketOpen(fn: (res: _SocketOnOpenRes) => void): void;\n    export function onSocketClose(fn: (res: _SocketOnCloseRes) => void): void;\n    export function onSocketError(fn: (res: _SocketOnErrorRes) => void): void;\n    export function onSocketMessage(fn: (res: _SocketOnMessageRes) => void): void;\n\n    export function vibrateShort(data: _VibrateCallbackObj): void;\n    export function onShareTimeline(fn: Function): void;\n\n    /**\n     * 当小程序启动，或从后台进入前台显示，会触发 onShow\n     */\n    export function onShow(fn: Function): void;\n    export function offShow(fn: Function): void;\n    /**\n     * 当小程序隐藏，或进入后台，会触发 onHide\n     */\n    export function onHide(fn: Function): void;\n    export function offHide(fn: Function): void;\n    export function onError(fn: { message: string; stack: string }): void;\n    export function onUnhandledRejection(fn: { reason: string; promise: Promise }): void;\n\n    export function triggerGC(): void;\n    export function onMemoryWarning(fn: Function): void;\n    export function getExptInfoSync(keys?: string[]);\n\n    /**\n     * 批量添加卡券。\n     */\n    export function exitMiniProgram(object?: Object): void;\n    export function restartMiniProgram(object?: Object): void;\n    export function createCanvas(): any;\n    export function createImage(): any;\n    export function requestSubscribeWhatsNew(object: Object): void;\n    export function getWhatsNewSubscriptionsSetting(object: Object): void;\n    export function requestSubscribeSystemMessage(object: Object): void;\n\n    export function getUserInfo(object: _getUserInfoObject): void;\n\n    export const aldStage: _AldStageInterface;\n\n    /**\n     * 创建并返回内部 audio 上下文 `innerAudioContext` 对象。*本接口是 `wx.createAudioContext` 升级版。*\n     */\n    export function createInnerAudioContext(): _innerAudioContextObject;\n\n    export function createAppBox(data: _createAppBoxObject);\n\n    export function createBannerAd(object: _BannerAdObject): any;\n\n    export function addToDesktop(object: _CallbackObj): any;\n\n    export function saveAppToDesktop(object: _CallbackObj): any;\n\n    export function addColorSign(object: _CallbackObj): any;\n    export function getFuzzyLocation(object: {}): any;\n\n    /**\n     * 向开放数据域发送消息\n     */\n    export function postMessage(object: Object): void;\n    export function hideLoading(): void;\n    export function showLoading(object: Object): void;\n    export function downloadFile(object: Object): void;\n    export function saveImageToPhotosAlbum(object: Object): void;\n\n    /**\n     * 对用户托管数据进行写数据操作。允许同时写多组 KV 数据。\n     */\n    export function setUserCloudStorage(object: _setUserCloudStorageObject): void;\n\n    export function getOpenDataContext(): _OpenDataContext;\n\n    export function getSetting(object: _CallbackObj & { withSubscriptions: boolean }): void;\n\n    /**\n     * 调起客户端小程序设置界面，返回用户设置的操作结果。\n     */\n    export function openSetting(object: _CallbackObj): void;\n\n    export function subscribeAppMsg(object: _SubscribeAppMsg);\n\n    /**\n     * 获取当前用户的游戏圈数据\n     * @param object\n     */\n    export function getGameClubData(object: Object): void;\n\n    export function getSetting(Object: Object): void;\n\n    export function getFileSystemManager(): any;\n    export const env = { USER_DATA_PATH: string };\n    export function getSystemInfoSync(): Object;\n\n    export function onAddToFavorites(any): Object;\n}\n\ninterface _MidasPaymentGameItemObject {\n    paySig: string;\n    signData: string;\n    signature: string;\n\n    /**\n     * 接口调用失败的回调函数\n     */\n    fail?: (err: { errMsg; errCode }) => void;\n\n    /**\n     * 接口调用结束的回调函数（调用成功、失败都会执行）\n     */\n    complete?: () => void;\n\n    /**\n     * 接口取消的回调\n     */\n    success?: (res, errCode) => void;\n}\n\ninterface _NetworkStatusChange {\n    // 当前是否有网络连接\n    isConnected: boolean;\n    // 网络类型\n    networkType: string;\n}\n\ninterface _SubscribeAppMsg {\n    subscribe: boolean;\n    /**\n     * 接口调用成功的回调函数\n     */\n    success?: (ret?: any) => void;\n\n    /**\n     * 接口调用失败的回调函数\n     */\n    fail?: (err: any) => void;\n}\n\ninterface _VibrateCallbackObj {\n    type: string;\n    /**\n     * 接口调用成功的回调函数\n     */\n    success?: (ret?: any) => void;\n\n    /**\n     * 接口调用失败的回调函数\n     */\n    fail?: (err: any) => void;\n\n    /**\n     * 接口调用结束的回调函数（调用成功、失败都会执行）\n     */\n    complete?: (err: any) => void;\n}\n\ninterface _CallbackObj {\n    /**\n     * 接口调用成功的回调函数\n     */\n    success?: (ret?: any) => void;\n\n    /**\n     * 接口调用失败的回调函数\n     */\n    fail?: (err: any) => void;\n\n    /**\n     * 接口调用结束的回调函数（调用成功、失败都会执行）\n     */\n    complete?: (err: any) => void;\n}\n\ninterface _setUserCloudStorageObject {\n    /**\n     * 要修改的 KV 数据列表\n     */\n    KVDataList: Array<_kvDataObject>;\n    /**\n     * 接口调用成功的回调函数\n     */\n    success?: () => void;\n    /**\n     * 接口调用失败的回调函数\n     */\n    fail?: () => void;\n    /**\n     * 接口调用结束的回调函数（调用成功、失败都会执行）\n     */\n    complete?: () => void;\n}\n\ninterface _kvDataObject {\n    /**\n     * 数据的key\n     */\n    key: string;\n    /**\n     * 数据的value\n     */\n    value: string;\n}\n\ninterface _OpenDataContext {\n    canvas: any;\n\n    postMessage(obj);\n}\n\ninterface _createAppBoxObject {\n    adUnitId: string;\n}\n\ninterface _AldStageInterface {\n    /**\n     * 关卡开始\n     * @param obj\n     */\n    onStart(obj: { stageId: string; stageName: string; userId?: string }): void;\n\n    /**\n     * 捕捉用户在关卡中的一些行为和操作\n     * @param obj {\n     *    event: 事件类型  payStart:发起支付、paySuccess:支付成功、payFail:支付失败、tools:使用道具、revive:复活、award:奖励\n     * }\n     */\n    onRunning(obj: { stageId: string; stageName: string; userId?: string; event: string; params?: any }): void;\n\n    /**\n     * 捕捉用户在关卡中的一些操作\n     * @param obj {\n     *    event: 事件类型  complete:关卡完成、fail:关卡失败\n     * }\n     */\n    onEnd(obj: { stageId: string; stageName: string; userId?: string; event: string }): void;\n}\n\ninterface _InnerAudioContextObject {\n    /**\n     * 音频资源的地址，用于直接播放。2.2.3 开始支持云文件ID\n     */\n    src: string;\n    /**\n     * 开始播放的位置（单位：s），默认为 0\n     */\n    startTime: number;\n    /**\n     * 是否自动开始播放，默认为 false\n     */\n    autoplay: boolean;\n    /**\n     * 是否循环播放，默认为 false\n     */\n    loop: boolean;\n    /**\n     * 是否遵循系统静音开关，默认为 true。当此参数为 false 时，即使用户打开了静音开关，也能继续发出声音。从 2.3.0 版本开始此参数不生效，使用 wx.setInnerAudioOption 接口统一设置。\n     */\n    obeyMuteSwitch: boolean;\n    /**\n     * 音量。范围 0~1。默认为 1\n     */\n    volume: number;\n    /**\n     * 当前音频的长度（单位 s）。只有在当前有合法的 src 时返回（只读）\n     */\n    duration: number;\n    /**\n     * 当前音频的播放位置（单位 s）。只有在当前有合法的 src 时返回，时间保留小数点后 6 位（只读）\n     */\n    currentTime: number;\n    /**\n     * 当前是是否暂停或停止状态（只读）\n     */\n    paused: boolean;\n    /**\n     * 音频缓冲的时间点，仅保证当前播放时间点到此时间点内容已缓冲（只读）\n     */\n    buffered: number;\n    /**\n     * 播放\n     */\n    play();\n    /**\n     * 暂停。暂停后的音频再播放会从暂停处开始播放\n     */\n    pause();\n    /**\n     * 停止。停止后的音频再播放会从头开始播放。\n     */\n    stop();\n    /**\n     * 跳转到指定位置\n     */\n    seek(position: number);\n    /**\n     * 销毁当前实例\n     */\n    destroy();\n    /**\n     * 监听音频进入可以播放状态的事件。但不保证后面可以流畅播放\n     */\n    onCanplay(callback: () => void);\n    /**\n     * 取消监听音频进入可以播放状态的事件\n     */\n    offCanplay(callback: () => void);\n    /**\n     * 监听音频播放事件\n     */\n    onPlay(callback: () => void);\n    /**\n     * 取消监听音频播放事件\n     */\n    offPlay(callback: () => void);\n    /**\n     * 监听音频暂停事件\n     */\n    onPause(callback: () => void);\n    /**\n     * 取消监听音频暂停事件\n     */\n    offPause(callback: () => void);\n    /**\n     * 监听音频停止事件\n     */\n    onStop(callback: () => void);\n    /**\n     * 取消监听音频停止事件\n     */\n    offStop(callback: () => void);\n    /**\n     * 监听音频自然播放至结束的事件\n     */\n    onEnded(callback: () => void);\n    /**\n     * 取消监听音频自然播放至结束的事件\n     */\n    offEnded(callback: () => void);\n    /**\n     * 监听音频播放进度更新事件\n     */\n    onTimeUpdate(callback: () => void);\n    /**\n     * 取消监听音频播放进度更新事件\n     */\n    offTimeUpdate(callback: () => void);\n    /**\n     * 监听音频播放错误事件\n     */\n    onError(callback: () => void);\n    /**\n     * 取消监听音频播放错误事件\n     */\n    offError(callback: () => void);\n\n    /**\n     * 监听音频加载中事件。当音频因为数据不足，需要停下来加载时会触发\n     */\n    onWaiting(callback: () => void);\n    /**\n     * 取消监听音频加载中事件\n     */\n    offWaiting(callback: () => void);\n    /**\n     * 监听音频进行跳转操作的事件\n     */\n    onSeeking(callback: () => void);\n    /**\n     * 取消监听音频进行跳转操作的事件\n     */\n    offSeeking(callback: () => void);\n    /**\n     * 监听音频完成跳转操作的事件\n     */\n    onSeeked(callback: () => void);\n    /**\n     * 取消监听音频完成跳转操作的事件\n     */\n    offSeeked(callback: () => void);\n}\n\ninterface _BannerAdObject {\n    adUnitId: string;\n    adIntervals?: number;\n    style: _StyleObject;\n}\n\ninterface _LoginObject {\n    /**\n     * 接口调用成功的回调函数\n     */\n    success: (result: _LoginSuccessObject) => void;\n\n    /**\n     * 接口调用失败的回调函数\n     */\n    fail?: (err?) => void;\n\n    /**\n     * 接口调用结束的回调函数（调用成功、失败都会执行）\n     */\n    complete?: () => void;\n}\n\ninterface _GameClubObject {\n    dataTypeList: Array<_GameClubTypeObject>;\n    success?: () => {};\n    fail?: () => {};\n    complete?: () => {};\n}\n\ninterface _GameClubTypeObject {\n    type: number;\n    subKey: string;\n}\n\ninterface _GameClubResDate {\n    signature: string;\n    encryptedData: string;\n    iv: string;\n    cloudID: string;\n}\n"]], 0, 0, [], [], []]