require("adapter-min.js"), __globalAdapter.init();

var e = require("./engine-loader");

function a() {
    require("cocos/cocos2d-js-min.js"), __globalAdapter.adaptEngine(), require("./ccRequire"), 
    require("./src/settings"), require("./main"), cc.view._maxPixelRatio = 4, cc.sys.platform !== cc.sys.WECHAT_GAME_SUB && (cc.macro.CLEANUP_IMAGE_CACHE = !0), 
    e.loadEngine("main_scripts", function() {
        e.loadEngine("ab_scripts", r);
    });
}

function r() {
    e.loadEngine("qzz_sdk", function() {
        require("./sdk/entry"), window.boot();
    });
}

var n = "assets/start-scene/native/aa/aa35290a-f5ae-4138-955e-aecb6489d5d1.8ae84.jpg";

try {
    var t = wx.getStorageSync("loading_state");
    if (t) {
        var i = 1, c = JSON.parse(t);
        c && (i = +c.value || 2), 2 === i && (n = "assets/start-scene/native/aa/aa35290a-f5ae-4138-955e-aecb6489d5d1.8ae84.jpg");
    }
} catch (e) {}

if (function(e, a) {
    e = e.split("."), a = a.split(".");
    for (var r = Math.max(e.length, a.length); e.length < r; ) e.push("0");
    for (;a.length < r; ) a.push("0");
    for (var n = 0; n < r; n++) {
        var t = parseInt(e[n]), i = parseInt(a[n]);
        if (t > i) return 1;
        if (t < i) return -1;
    }
    return 0;
}(wx.getSystemInfoSync().SDKVersion, "2.1.0") > -1) {
    try {
        GameGlobal.LoadingManager = requirePlugin("MinigameLoading", {
            customEnv: {
                wx: wx,
                canvas: canvas
            }
        }).default, GameGlobal.LoadingManager.create({
            images: [ {
                src: n
            } ],
            contextType: "webgl",
            contextAttributes: {
                alpha: !1,
                antialias: !1,
                depth: !0,
                desynchronized: !1,
                failIfMajorPerformanceCaveat: !1,
                powerPreference: "default",
                premultipliedAlpha: !0,
                preserveDrawingBuffer: !1,
                stencil: !0,
                xrCompatible: !1
            }
        }).then(function() {}).catch(function(e) {
            console.error("封面图显示失败");
        });
    } catch (e) {
        console.error("当前环境不支持使用插件", e);
    }
    e.loadEngine("cocos", function() {
        return a();
    });
} else a();