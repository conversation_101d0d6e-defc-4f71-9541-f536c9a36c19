function e(e, a) {
    e = e.split("."), a = a.split(".");
    for (var n = Math.max(e.length, a.length); e.length < n; ) e.push("0");
    for (;a.length < n; ) a.push("0");
    for (var t = 0; t < n; t++) {
        var s = parseInt(e[t]), r = parseInt(a[t]);
        if (s > r) return 1;
        if (s < r) return -1;
    }
    return 0;
}

window.boot = function() {
    var a = window._CCSettings;
    window._CCSettings = void 0;
    var n = function() {
        cc.view.enableRetina(!0), cc.view.resizeWithBrowserSize(!0);
        var n = a.launchScene;
        cc.director.loadScene(n, null, function() {
            GameGlobal.LoadingManager && !GameGlobal.LoadingManager.isMainCanvas && GameGlobal.LoadingManager.destroy();
        });
        var t = wx.getSystemInfoSync();
        if ("ios" === t.platform && GameGlobal.isIOSHighPerformanceMode) {
            var s = t.system.split(" ")[1];
            -1 === e(s, "15.4") && (console.log("执行 iOS 共享 buffer 问题修复"), cc.MeshBuffer.prototype.checkAndSwitchBuffer = function(e) {
                this.vertexOffset + e > 65535 && (this.uploadData(), this._batcher._flush());
            }, cc.MeshBuffer.prototype.forwardIndiceStartToOffset = function() {
                this.uploadData(), this.switchBuffer();
            }), -1 === e(s, "15.0") && canvas.getContext("webgl", {
                antialias: !0
            });
        }
    }, t = cc.sys.platform === cc.sys.WECHAT_GAME_SUB, s = {
        id: "GameCanvas",
        debugMode: a.debug ? cc.debug.DebugMode.INFO : cc.debug.DebugMode.ERROR,
        showFPS: !t && a.debug,
        frameRate: 60,
        groupList: a.groupList,
        collisionMatrix: a.collisionMatrix
    };
    cc.assetManager.init({
        bundleVers: a.bundleVers,
        subpackages: a.subpackages,
        remoteBundles: a.remoteBundles,
        server: a.server,
        subContextRoot: a.subContextRoot
    });
    var r = cc.AssetManager.BuiltinBundleName.RESOURCES, o = cc.AssetManager.BuiltinBundleName.INTERNAL, i = cc.AssetManager.BuiltinBundleName.MAIN, c = cc.AssetManager.BuiltinBundleName.START_SCENE, u = [ o ];
    a.hasResourcesBundle && u.push(r), a.hasStartSceneBundle && u.push(i);
    var l = 0;
    function g(e) {
        if (e) return console.error(e.message, e.stack);
        ++l === u.length + 1 && cc.assetManager.loadBundle(a.hasStartSceneBundle ? c : i, function(e) {
            e || (GameGlobal.LoadingManager && GameGlobal.LoadingManager.isMainCanvas ? GameGlobal.LoadingManager.destroy().then(function() {
                cc.game.run(s, n);
            }) : cc.game.run(s, n));
        });
    }
    cc.assetManager.loadScript(a.jsList.map(function(e) {
        return "src/" + e;
    }), g);
    for (var d = 0; d < u.length; d++) cc.assetManager.loadBundle(u[d], g);
};